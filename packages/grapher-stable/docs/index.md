# Table of Contents

Let's learn Grapher. It's quite easy and it's time to change the way you think about your data.

Before we dive into Grapher you need to know the fundamentals of Meteor, but not mandatory, you can
learn the principles either way.

*   http://www.meteor-tuts.com/chapters/1/intro.html
*   http://docs.meteor.com/api/collections.html
*   http://docs.meteor.com/api/methods.html
*   http://docs.meteor.com/api/pubsub.html

### [Introduction](introduction.md)

This introduces you to the way we use Grapher in the most basic form, without relationships.

### [Linking Collections](linking_collections.md)

Learn the various ways of linking collections to each other.

### [Linker Engine](linker_engine.md)

Learn about how you can programatically set and fetch related data.

### [Query Options](query_options.md)

Learn more advanced ways to use your queries.

### [Reducers](reducers.md)

Remove complexity from your queries, make them a breeze by using reducers.

### [Named Queries](named_queries.md)

Learn the right way to define and expose queries to the client in a secure manner.

### [Hypernova](hypernova.md)

Read about the tool that makes Grapher so performant.

### [Denormalization](denormalization.md)

Learn how to denormalize your data to enable even more performance, and super advanced searching.

### [Caching Results](caching_results.md)

Leverage the power of Grapher to cache the most heavy or frequently used queries.

### [Global Exposure](global_exposure.md)

Learn about a neat way to expose your collections. This was the initial approach of Grapher, and it is very useful
for when you want to expose an API accessing most parts of the database to your clients.

### [Structure & Patterns](structure_and_patterns.md)

Learn about some good ways to structure your code some and about common pitfalls.

### [Outside Meteor](outside_meteor.md)

If you want to use Grapher in a React Native app so from any other language as an API, it is possible

### [GraphQL Bridge](graphql.md)

If you want to use Grapher in a React Native app so from any other language as an API, it is possible

### [API](api.md)

After you've learned about Grapher, here's your cheatsheet.
