import { create } from 'zustand'

const useAudioRecorderStore = create(set => ({
	status: 'idle', // 'idle' | 'recording' | 'playing' | 'processing'
	audioUrl: null,
	audioBlob: null,
	duration: 0,
	error: null,
	isReady: false,
	init: () => set({ isReady: true }),
	startRecording: () => set({ status: 'recording' }),
	stopRecording: () => set({ status: 'processing' }),
	setRecordedAudio: audio =>
		set({ status: 'idle', audioUrl: audio.url, audioBlob: audio.blob, duration: audio.duration }),
	playRecordedAudio: () => set({ status: 'playing' }),
	stopRecordedAudio: () => set({ status: 'idle' }),
	clearRecordedAudio: () => set({ status: 'idle', audioUrl: null, audioBlob: null }),
	setError: error => set({ error, status: 'idle' }),
}))

export default useAudioRecorderStore
