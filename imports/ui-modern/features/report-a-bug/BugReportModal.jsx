import React, { useState } from 'react'
import { BugReportButton } from '@components/prefabs/bug-report/BugReportButton'
import { BugReport } from '@components/prefabs/bug-report/BugReport'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'
import { Button } from '@mui/material'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faPaperPlane, faPlay, faStop, faTrash } from '@fortawesome/pro-regular-svg-icons'
import { isEmpty } from 'lodash'
import moment from 'moment'
import { useAudioRecorder } from '../audio-recorder/hooks/useAudioRecorder'
import { saveReportData } from './services/report-a-bug.services'
import { Meteor } from 'meteor/meteor'

export default function BugReportModal({ call }) {
	const [loading, setLoading] = useState(false)
	const [events, setEvents] = useState([])
	const [recording, setRecording] = useState(false)
	const { audioUrl, audioBlob, startRecording, stopRecording, playAudio, isPlaying, stopAudio, clearAudio } =
		useAudioRecorder({
			onRecordingStop: audioData => {
				console.log('audioData', audioData)
			},
			onError: error => {
				console.error(error)
			},
		})

	const clearReport = () => {
		clearAudio()
		setEvents([])
	}

	const sendBugReport = async () => {
		setLoading(true)
		const report = await saveReportData(events, audioBlob)
		await Meteor.callAsync('sendReportEmail', {
			...report,
			location: window.location.href,
		})
		clearReport()
		setLoading(false)
		call.end(true)
	}

	return (
		<Stack
			className="no-record"
			sx={{
				mr: {
					xs: 0.5,
					sm: 2,
				},
				mt: {
					xs: 10,
					md: 2,
				},
				width: {
					xs: '98vw',
					sm: '360px',
				},
				alignItems: 'center',
				opacity: recording ? 0.3 : 1,
				'&:hover': {
					opacity: '1 !important',
					transition: 'opacity 0.3s',
				},
			}}>
			<Stack
				sx={{
					width: '100%',
					backgroundColor: 'white',
					borderRadius: 3,
					p: 2,
					boxShadow: 'rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px',
				}}>
				<Typography
					variant="subtitle2"
					sx={{
						fontWeight: 'bold',
						fontSize: '14px',
					}}>
					Report a bug
				</Typography>
				<Typography
					sx={{
						fontSize: '12px',
						mb: 1,
					}}>
					Please share the steps to reproduce the bug.
				</Typography>
				{(isEmpty(events) || !audioUrl) && (
					<BugReportButton
						onStartRecording={async () => {
							setEvents([])
							await startRecording()
							setRecording(true)
						}}
						onComplete={async events => {
							setEvents(events)
							await stopRecording()
							setRecording(false)
						}}
					/>
				)}
				{audioUrl && (
					<>
						{events.length > 0 && (
							<BugReport
								events={events}
								isPlaying={isPlaying}
							/>
						)}
						<Stack
							sx={{
								backgroundColor: 'var(--gray-100)',
								flexDirection: 'row',
								alignItems: 'center',
								justifyContent: 'space-between',
								mt: 0.5,
								p: 0.5,
								borderRadius: 3,
							}}>
							<Button
								sx={{
									borderRadius: 2,
								}}
								onClick={isPlaying ? stopAudio : playAudio}>
								<FontAwesomeIcon icon={isPlaying ? faStop : faPlay} />
							</Button>
							<Typography
								sx={{
									fontSize: '10px',
								}}>
								{'bug-report-' + moment(Date.now()).format('DDMMYY-hhmmss') + '.mp4'}
							</Typography>
							<Button
								sx={{
									borderRadius: 2,
								}}
								onClick={clearAudio}
								color="error">
								<FontAwesomeIcon icon={faTrash} />
							</Button>
						</Stack>
						<Button
							variant="contained"
							onClick={sendBugReport}
							disabled={loading}
							sx={{
								textTransform: 'none',
								fontSize: '12px',
								mt: 1,
								borderRadius: 2,
								boxShadow: 'none',
							}}>
							<FontAwesomeIcon
								icon={faPaperPlane}
								style={{ marginRight: '8px' }}
							/>
							{loading ? 'Sending' : 'Send'}
						</Button>
						<Button
							color="gray"
							onClick={() => {
								clearReport()
								call.end(false)
							}}
							sx={{
								mt: 1,
								textTransform: 'none',
								fontSize: '12px',
								borderRadius: 2,
							}}>
							Cancel
						</Button>
					</>
				)}
			</Stack>
		</Stack>
	)
}
