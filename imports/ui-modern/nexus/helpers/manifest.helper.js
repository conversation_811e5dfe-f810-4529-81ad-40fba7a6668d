import { Meteor } from 'meteor/meteor'
import Logger from '@ui-libs/logger/logger'
import { publicSettings } from '../../../shared/dev/public-settings/public-settings'

const logger = new Logger({
	tag: 'client-meta-tags',
})

function fetchManifest(href) {
	// This function fetches the manifest file from the given URL and returns a promise that resolves to the JSON content of the manifest.
	// Is required to fetch the manifest file from the server and install PWA with custom branding
	return fetch(href, {
		method: 'GET',
		headers: {
			'Content-Type': 'application/json',
		},
	})
		.then(response => {
			if (!response.ok) {
				throw new Error('Network response was not ok')
			}
			return response.json()
		})
		.catch(error => {
			logger.error(error)
			return null
		})
}

export const injectManifest = (slug, coachId, clientId) => {
	try {
		const version = publicSettings.get('app.version')
		const href = Meteor.absoluteUrl(
			`api/manifest?coachId=${coachId}&clientId=${clientId}&isMultiInstall=${Meteor.userId() === coachId}&slug=${slug}&version=${version}`,
		)
		const $head = document.head
		const $manifest = document.createElement('link')
		fetchManifest(href).then(_manifest => {
			logger.info(_manifest)
			$manifest.rel = 'manifest'
			$manifest.href = href
			$head.appendChild($manifest)
		})
		return true
	} catch (error) {
		console.error('Error creating meta tags', error)
		return false
	}
}
