import UAParser from 'ua-parser-js'

/**
 * Checks if the current environment is a browser.
 * @returns {boolean} True if in a browser environment, false otherwise.
 */
const isBrowser = () => typeof window !== 'undefined' && typeof navigator !== 'undefined'

/**
 * Checks if the browser is likely an embedded WebView (Android wv or iOS non-Safari).
 * @returns {boolean} True if likely a WebView, false otherwise or if not in a browser.
 */
export const isWebView = () => {
	if (!isBrowser()) return false
	const userAgent = navigator.userAgent
	// Android WebView check (common 'wv' token)
	if (/\bwv\b/i.test(userAgent)) {
		return true
	}
	// iOS WebView check (common pattern: Mobile/ but no Safari/)
	return /(iPhone|iPod|iPad).*AppleWebKit(?!.*Safari)/i.test(userAgent)
}

/**
 * Gets parsed User Agent details using UAParser.
 * @returns {Object|null} The parsed UA details or null if not in a browser environment.
 */
const getUADetails = () => {
	if (!isBrowser()) return null
	const ua = new UAParser()
	return ua.getResult()
}

/**
 * Gets network connection details.
 * @returns {Object|null} The network details or null if not in a browser environment.
 */
const getNetworkDetails = () => {
	if (!isBrowser()) return null
	return navigator.connection || null
}

/**
 * Gets screen details.
 * @returns {Object|null} The screen details or null if not in a browser environment.
 */
const getScreenDetails = () => {
	if (!isBrowser()) return null
	return {
		width: window.screen.width,
		height: window.screen.height,
		colorDepth: window.screen.colorDepth,
	}
}

/**
 * Gets browser capabilities.
 * @returns {Object|null} The capabilities details or null if not in a browser environment.
 */
const getCapabilitiesDetails = () => {
	if (!isBrowser()) return null
	return {
		touchSupport: 'ontouchstart' in window,
		cookiesEnabled: navigator.cookieEnabled,
		doNotTrack: navigator.doNotTrack === '1' || window.doNotTrack === true,
		languages: navigator.languages,
		timeZone: Intl.DateTimeFormat().resolvedOptions().timeZone,
	}
}

/**
 * Gets a complete report of device information including if it's an embedded WebView.
 * @returns {Object|null} Device information or null if not in a browser.
 */
export function getDeviceDetails() {
	if (!isBrowser()) {
		console.warn('Browser APIs not available')
		return null
	}
	const details = getUADetails()
	const capabilities = getCapabilitiesDetails()
	const screen = getScreenDetails()
	const network = getNetworkDetails()
	const isEmbeddedWebView = isWebView() // Check if it's a WebView

	return {
		...details,
		...capabilities,
		screen,
		network,
		isEmbeddedWebView, // Add the WebView status
	}
}

/**
 * Checks various browser permissions (camera, microphone, notifications).
 * @returns {Promise<Object|undefined>} An object with permission states or undefined if an error occurs or not in a browser.
 */
export async function checkPermissions() {
	if (!isBrowser() || !navigator.permissions) {
		console.warn('Browser Permissions API not available')
		return undefined
	}
	const permissions = {}
	try {
		const camera = await navigator.permissions.query({ name: 'camera' })
		permissions.camera = camera.state
		const microphone = await navigator.permissions.query({ name: 'microphone' })
		permissions.microphone = microphone.state
		const notifications = await navigator.permissions.query({ name: 'notifications' })
		permissions.notifications = notifications.state
		return permissions
	} catch (error) {
		console.warn('Error checking permissions:', error)
		return undefined
	}
}

/**
 * Gets the lowercased browser name.
 * @returns {string} The browser name or 'unknown' if not in a browser or name not found.
 */
export function getBrowser() {
	const details = getUADetails()
	return details?.browser?.name?.toLowerCase() || 'unknown'
}

export function isIosDevice() {
	const details = getUADetails()
	return (
		details?.os?.name?.toLowerCase() === 'ios' ||
		details?.os?.name?.toLowerCase() === 'ipad' ||
		details?.os?.name?.toLowerCase() === 'iphone' ||
		details?.os?.name?.toLowerCase() === 'ipod' ||
		details?.os?.name?.toLowerCase() === 'mac os'
	)
}

export const validateNotifications = () => {
	return (
		typeof window !== 'undefined' &&
		'serviceWorker' in navigator &&
		'PushManager' in window &&
		'Notification' in window &&
		'fetch' in window &&
		ServiceWorkerRegistration.prototype.hasOwnProperty('showNotification') &&
		PushSubscription.prototype.hasOwnProperty('getKey')
	)
}

const isChromeAppInstalled =
	window.matchMedia('(display-mode: standalone)').matches || window.navigator.standalone === true
export const isSafariAppInstalled = isIosDevice() && validateNotifications()

export const isPWA = isChromeAppInstalled || isSafariAppInstalled

export function isSafariDesktopBrowser() {
	if (isIosDevice()) {
		return false
	}
	const ua = navigator.userAgent?.toLowerCase()
	const version = parseInt(ua.split('version/')[1], 10)
	return ua.includes('safari') && !ua.includes('chrome') && !ua.includes('android') && version >= 17
}
