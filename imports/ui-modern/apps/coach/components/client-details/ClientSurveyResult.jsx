import Grid from '@mui/material/Grid2'
import Typography from '@mui/material/Typography'
import React from 'react'
import {
	faCircle1,
	faCircle2,
	faCircle3,
	faCircle4,
	faFace<PERSON>rown,
	faF<PERSON><PERSON><PERSON>,
	faFaceMeh,
	faFaceSmile,
} from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import Stack from '@mui/material/Stack'
import { FeedbackCardHeader } from './FeedbackCardHeader'

const customIcons = {
	1: {
		emoji: faFaceFrown,
		number: faCircle1,
		label: 'Very Dissatisfied',
	},
	2: {
		emoji: faFaceMeh,
		number: faCircle2,
		label: 'Dissatisfied',
	},
	3: {
		emoji: faFaceSmile,
		number: faCircle3,
		label: 'Satisfied',
	},
	4: {
		emoji: faF<PERSON><PERSON><PERSON>,
		number: faCircle4,
		label: 'Very Satisfied',
	},
}

export function ClientSurveyResult({ workout, createdAt, clientId, responseQuestionnaire, coachQuestionnaire }) {
	const iconStyle = coachQuestionnaire.iconStyle
	return (
		<Grid
			container
			sx={{
				borderBottom: '1px solid var(--gray-300)',
				paddingBottom: 2,
				'&:last-child': {
					borderBottom: 'none',
					paddingBottom: 0,
				},
			}}
			spacing={1}>
			<FeedbackCardHeader
				workout={workout}
				createdAt={createdAt}
				clientId={clientId}
			/>
			<Grid
				size={12}
				sx={{
					display: 'flex',
					flexWrap: 'wrap',
					gap: 3,
					mt: 4,
					mb: 4,
					alignItems: 'center',
					'& > div': {
						flex: '1 1 200px',
					},
				}}>
				{responseQuestionnaire
					.filter(response => response.enable === true)
					.map((response, index) => (
						<Grid
							key={`quest-${index}`}
							sx={{
								display: 'flex',
								flexDirection: 'column',
								alignItems: 'center',
								justifyContent: 'space-between',
								gap: 1.5,
								height: { xs: 'auto', lg: '100%' },
								mt: 1,
							}}>
							<Typography
								variant="body"
								sx={{ textAlign: 'center', fontWeight: 600, color: 'gray.500' }}>
								{response?.question}
							</Typography>
							<Stack
								direction="row"
								sx={{
									alignItems: 'center',
									justifyContent: 'center',
									'& svg': {
										height: '42px',
										width: '42px',
										backgroundColor: '#FFCC4D',
										borderRadius: '50%',
									},
								}}>
								<FontAwesomeIcon icon={customIcons[response?.response ?? 3][iconStyle]} />
							</Stack>
						</Grid>
					))}
			</Grid>
		</Grid>
	)
}
