import React from 'react'
import { NotificationsCenter } from '@ui-common/components/notifications/NotificationsCenter'
import { SubscribeBanner } from '@ui-common/components/notifications/SubscribeBanner'
import { ModalContent } from '@ui-libs/modal-portal/default-variants/ModalContent'
import MenuItem from '@mui/material/MenuItem'
import Divider from '@mui/material/Divider'
import { useNavigate } from 'react-router-dom'
import { MethodCall } from '@lib/method/method'
import { useQueryClient } from '@tanstack/react-query'
import Switch from '@mui/material/Switch'
import { useWebPush } from '@app-nexus/hooks/useWebPush'
import { ModalTabs } from '@ui-libs/modal-portal/components/ModalTabs'
import { useTabs } from '@ui-common/components/control/useTabs'
import { ToDoCenter } from '@app-coach/components/to-do-list/ToDoCenter'
import { useSubscription } from '/imports/ui/context/subscription/SubscriptionProvider'
import { isPWA } from '@app-nexus/helpers/device.helper'
import { toast } from 'react-toastify'

export function NotificationsCenterCoach({ call, userId }) {
	const navigate = useNavigate()
	const queryClient = useQueryClient()
	const { hasSubscription } = useSubscription()
	const { subscribe, unsubscribe, isSubscribed } = useWebPush({
		scope: '/app/',
		userId,
	})
	const tabsControl = useTabs({
		tabs: ['Notifications', 'To-Do List'],
		defaultTab: 'notifications',
	})
	const markAllAsRead = async () => {
		const [error] = await MethodCall('notification.markAllAsRead', { userId })
		if (error) {
			return toast.error(error.message)
		}
		await queryClient.invalidateQueries({ queryKey: ['notifications', 'notificationsCount'] })
	}
	const markAllAsUnread = async () => {
		const [error] = await MethodCall('notification.markAllAsUnread', { userId })
		if (error) {
			return toast.error(error.message)
		}
		await queryClient.invalidateQueries({ queryKey: ['notifications', 'notificationsCount'] })
	}
	const goToSettings = () => {
		navigate('/app/account#pushNotifications')
		call.end()
	}
	return (
		<ModalContent
			title={'Notifications Center'}
			slots={{
				subheader: (
					<ModalTabs
						{...tabsControl}
						sx={{
							mt: 0,
						}}
					/>
				),
				footer: tabsControl.tab.value === 'notifications' && hasSubscription && isPWA && (
					<SubscribeBanner
						scope={'/app/'}
						userId={userId}
					/>
				),
			}}
			menu={[
				<MenuItem
					key="mark-all-as-read"
					onClick={markAllAsRead}>
					Mark all as read
				</MenuItem>,
				<MenuItem
					key="mark-all-as-unread"
					onClick={markAllAsUnread}>
					Mark all as unread
				</MenuItem>,
				<Divider key="divider" />,
				<MenuItem
					key="settings"
					onClick={goToSettings}>
					Notification Settings
				</MenuItem>,
				<MenuItem
					key="push-notifications"
					onClick={() => {
						if (isSubscribed) {
							return unsubscribe()
						} else if (hasSubscription && isPWA) {
							return subscribe()
						}
					}}>
					Push Notifications
					<Switch
						key="push-notifications-switch"
						edge="end"
						sx={{
							ml: 1,
						}}
						checked={isSubscribed}
						disabled={!hasSubscription || !isPWA}
						color="primary"
						inputProps={{ 'aria-label': 'controlled' }}
						size="small"
					/>
				</MenuItem>,
			]}
			sx={{
				py: 0,
			}}
			call={call}>
			{tabsControl.tab.value === 'notifications' && <NotificationsCenter userId={userId} />}
			{tabsControl.tab.value === 'toDoList' && <ToDoCenter userId={userId} />}
		</ModalContent>
	)
}
