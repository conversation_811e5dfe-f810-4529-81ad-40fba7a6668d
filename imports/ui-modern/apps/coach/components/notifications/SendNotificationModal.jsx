import React from 'react'
import CustomTabs from '@design/components/control/Tabs/CustomTabs'
import useCustomTabs from '@design/components/control/Tabs/hooks/useCustomTabs'
import { SendNotificationForm } from '@ui-common/components/notifications/SendNotificationForm'
import ProBadge from '../../../../../ui/components/atoms/ProBadge'
import Modal from '@ui-libs/modal-portal/Modal'

export function SendNotificationModal({ call, clientId }) {
	const tabsController = useCustomTabs({
		tabs: ['Send to...', 'All'],
		defaultTab: 'Send to...',
	})
	return (
		<Modal>
			<Modal.Header
				title={'Send Notification'}
				subtitle={'Send notifications to clients'}
				icon={<ProBadge size="sm" />}
				onClose={call.end}
			/>
			{!clientId && (
				<Modal.SubHeader>
					<CustomTabs
						resetStyles
						controller={tabsController}
					/>
				</Modal.SubHeader>
			)}
			<Modal.Body>
				<SendNotificationForm
					allClients={tabsController.activeTab === 'all'}
					clientId={clientId}
					onClose={call.end}
				/>
			</Modal.Body>
		</Modal>
	)
}
