import React, { useEffect } from 'react'
import { Meteor } from 'meteor/meteor'
import { Helmet } from 'react-helmet-async'
import defaultBranding from '../../../../../ui/constants/DefaultBranding'
import { isIosDevice } from '@app-nexus/helpers/device.helper'

const encodeSpecialCharacters = str => {
	const characters = {
		"'": '%27',
		'(': '%28',
		')': '%29',
		'/': '%252F',
		',': '%2C',
		' ': '%20',
	}
	const replace = str.replace(/['()/, ]/g, c => characters[c])
	return encodeURI(replace)
}

export function CoachAppStartupImage() {
	const branding = defaultBranding
	const calculateDeviceSizes = () => {
		const splashscreenSize = {
			height: screen.height * devicePixelRatio,
			width: screen.width * devicePixelRatio,
		}
		const deviceSize = {
			height: screen.height,
			width: screen.width,
		}
		return { splashscreenSize, deviceSize, devicePixelRatio }
	}
	const device = isIosDevice() ? calculateDeviceSizes() : null
	const cloudName = Meteor.settings.public.cloudinary.cloud_name
	const iconPath = branding?.iconPath.replaceAll('/', ':')
	if (!device) {
		return null
	}
	const portraitConfigs = `upload/c_scale,h_${device.splashscreenSize.height},w_${device.splashscreenSize.width}/co_rgb:${branding.backgroundColor?.slice(1)},e_colorize/l_${iconPath}/c_scale,h_512,w_512/fl_layer_apply,g_center/co_rgb:${branding.themeColor.slice(1)},l_text:arial_${(device.splashscreenSize.height / 32).toFixed()}_normal_left:${encodeSpecialCharacters(branding?.appName)}/fl_layer_apply,g_south,y_${(device.splashscreenSize.height / 12).toFixed()}/resources/utils/white-box.jpg`
	const landscapeConfigs = `upload/c_scale,h_${device.splashscreenSize.width},w_${device.splashscreenSize.height}/co_rgb:${branding.backgroundColor?.slice(1)},e_colorize/l_${iconPath}/c_scale,h_512,w_512/fl_layer_apply,g_center/co_rgb:${branding.themeColor.slice(1)},l_text:arial_${(device.splashscreenSize.width / 32).toFixed()}_normal_left:${encodeSpecialCharacters(branding?.appName)}/fl_layer_apply,g_south,y_${(device.splashscreenSize.width / 12).toFixed()}/resources/utils/white-box.jpg`
	useEffect(() => {
		fetch(`https://res.cloudinary.com/${cloudName}/image/` + landscapeConfigs).catch(e => console.warn(e))
		fetch(`https://res.cloudinary.com/${cloudName}/image/` + portraitConfigs).catch(e => console.warn(e))
	}, [portraitConfigs, landscapeConfigs])
	return (
		<>
			<Helmet>
				<link
					rel="apple-touch-startup-image"
					media={`screen and (device-width: ${device.deviceSize.width}px) and (device-height: ${device.deviceSize.height}px) and (-webkit-device-pixel-ratio: ${device.devicePixelRatio}) and (orientation: landscape)`}
					href={`https://res.cloudinary.com/${cloudName}/image/` + landscapeConfigs}
				/>
				<link
					rel="apple-touch-startup-image"
					sizes={`${device.splashscreenSize.height}x${device.splashscreenSize.width}`}
					media={`screen and (device-width: ${device.deviceSize.width}px) and (device-height: ${device.deviceSize.height}px) and (-webkit-device-pixel-ratio: ${device.devicePixelRatio}) and (orientation: portrait)`}
					href={`https://res.cloudinary.com/${cloudName}/image/` + portraitConfigs}
				/>
			</Helmet>
		</>
	)
}
