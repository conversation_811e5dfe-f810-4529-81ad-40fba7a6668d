import React from 'react'
import Grid from '@mui/material/Grid2'
import Typography from '@mui/material/Typography'
import { styled } from '@mui/material/styles'
import { useParams } from 'react-router-dom'
import { useGrapherQuery } from '../../../../dev/create-query/useGrapherQuery'
import { getExercisePerformanceLogs } from '../../../../../api/exercisePerformanceLogs/exercisePerformanceLogs_Q'

const CustomWorkoutCard = styled(Grid)({
	border: '1px solid var(--gray-300)',
	boxShadow: 'none',
	borderRadius: 8,
	padding: 16,
	backgroundColor: 'white',
})

const createAtTransform = obj => {
	const date = new Date(obj.createdAt)
	return date.toLocaleDateString('en-US', {
		year: 'numeric',
		month: 'long',
		day: '2-digit',
	})
}

export function PerformanceLogStats() {
	const { clientId, exerciseId } = useParams()

	const filters = {
		clientId,
		exerciseId,
	}

	const transform = {
		computedFields: {
			createdAt: createAtTransform,
		},
	}

	const { data: firstPerformance } = useGrapherQuery({
		query: getExercisePerformanceLogs,
		single: true,
		params: {
			filters,
			options: {
				sort: { createdAt: 1 },
			},
		},
		transform,
	})

	const { data: lastPerformance } = useGrapherQuery({
		query: getExercisePerformanceLogs,
		single: true,
		params: {
			filters,
			options: {
				sort: { createdAt: -1 },
			},
		},
		transform,
	})

	return (
		<Grid
			container
			spacing={2}>
			<CustomWorkoutCard
				size={{
					xs: 12,
					sm: 6,
					md: 4,
					lg: 3,
				}}>
				<Typography
					variant="body"
					sx={{
						fontWeight: 500,
						color: 'var(--gray-700)',
					}}>
					First
				</Typography>
				<Typography
					variant="caption"
					sx={{
						fontWeight: 500,
						color: 'var(--gray-400)',
					}}>
					{`${firstPerformance?.workout?.title} - ${firstPerformance?.createdAt}`}
				</Typography>
				<Typography
					variant="subtitle"
					sx={{
						color: 'var(--gray-700)',
						mt: 1,
					}}>
					{firstPerformance?.result ?? 'No Data'}
				</Typography>
			</CustomWorkoutCard>
			<CustomWorkoutCard
				size={{
					xs: 12,
					sm: 6,
					md: 4,
					lg: 3,
				}}>
				<Typography
					variant="body"
					sx={{
						fontWeight: 500,
						color: 'var(--gray-700)',
					}}>
					Last
				</Typography>
				<Typography
					variant="caption"
					sx={{
						fontWeight: 500,
						color: 'var(--gray-400)',
					}}>
					{`${lastPerformance?.workout?.title} - ${lastPerformance?.createdAt}`}
				</Typography>
				<Typography
					variant="subtitle"
					sx={{
						color: 'var(--gray-700)',
						mt: 1,
					}}>
					{lastPerformance?.result ?? 'No Data'}
				</Typography>
			</CustomWorkoutCard>
		</Grid>
	)
}
