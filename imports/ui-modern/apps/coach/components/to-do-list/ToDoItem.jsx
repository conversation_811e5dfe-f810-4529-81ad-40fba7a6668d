import React from 'react'
import Stack from '@mui/material/Stack'
import Checkbox from '@mui/material/Checkbox'
import Typography from '@mui/material/Typography'
import { ButtonOutlinedIcon } from '@ui-common/components/forms'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faTrashCan } from '@fortawesome/pro-regular-svg-icons'
import { useQueryClient } from '@tanstack/react-query'

export function ToDoItem({ item }) {
	const queryClient = useQueryClient()
	const markAsCompleted = async () => {
		await Meteor.callAsync('todoItems.toggleCompleted', {
			_id: item._id,
		})
		await queryClient.invalidateQueries({ queryKey: ['findTodoItems'] })
	}
	const deleteItem = async () => {
		await Meteor.callAsync('todoItems.delete', {
			_id: item._id,
		})
		await queryClient.invalidateQueries({ queryKey: ['findTodoItems'] })
	}

	return (
		<Stack
			direction="row"
			sx={{
				width: '100%',
				p: 1,
				border: '1px solid',
				borderColor: 'primary.300',
				gap: 1,
				alignItems: 'center',
				mb: 1.5,
				borderRadius: '8px',
				'&.MuiButtonBase-root': {
					opacity: 0.2,
				},
				'&:hover': {
					borderColor: 'primary.400',
					'& .MuiButtonBase-root': {
						opacity: 1,
					},
				},
			}}>
			<Checkbox
				checked={item.completed}
				onChange={markAsCompleted}
				color="primary"
				sx={{
					'&.MuiButtonBase-root': {
						opacity: '100%!important',
					},
				}}
			/>
			<Typography
				variant="body"
				sx={{
					fontWeight: 500,
					color: 'primary.700',
					textDecoration: item.completed ? 'line-through' : 'none',
				}}>
				{item.title}
			</Typography>
			{/* <Typography
				variant="body"
				sx={{
					ml: 'auto',
					color: 'gray.200',
				}}>
				{item.expireAt ? `Expires at ${item.expireAt}` : ''}
			</Typography> */}
			<ButtonOutlinedIcon
				sx={{
					ml: 'auto',
				}}
				color="error"
				onClick={deleteItem}>
				<FontAwesomeIcon
					icon={faTrashCan}
					size={'lg'}
				/>
			</ButtonOutlinedIcon>
		</Stack>
	)
}
