import React, { useEffect, useState } from 'react'
import Paper from '@mui/material/Paper'
import Table from '@mui/material/Table'
import TableContainer from '@mui/material/TableContainer'
import TableRow from '@mui/material/TableRow'
import TableCell from '@mui/material/TableCell'
import Typography from '@mui/material/Typography'
import TableHead from '@mui/material/TableHead'
import { NotificationsConfigsRow } from '@app-coach/pages/account-settings/notification-configs/NotificationsConfigsRow'
import TableBody from '@mui/material/TableBody'
import InputBase from '@mui/material/InputBase'
import Switch from '@mui/material/Switch'
import { styled } from '@mui/material/styles'
import { getNotificationsConfigs } from '@collections/notificationsConfigs/notificationsConfigs_Q'
import { useGrapherQuery } from '@ui-libs/create-query/useGrapherQuery'
import TableFooter from '@mui/material/TableFooter'
import Stack from '@mui/material/Stack'
import { openModal } from '@ui-libs/modal-portal'
import { useSubscription } from '../../../../../../ui/context/subscription/SubscriptionProvider'
import { StripeSubscriptionsModal } from '@app-coach/components/modals/StripeSubscriptionsModal'

export function NotificationsConfigsTable() {
	const { data: notificationConfigs, refetch } = useGrapherQuery({
		query: getNotificationsConfigs,
		single: true,
		params: { filters: { userId: Meteor.userId() } },
	})
	useEffect(() => {
		if (!notificationConfigs) {
			Meteor.callAsync('notificationsConfigs.create', { userId: Meteor.userId() })
				.then(() => {
					return refetch()
				})
				.catch(error => {
					console.log(error)
				})
		}
	}, [notificationConfigs])
	const { hasSubscription } = useSubscription()
	const [plansLowerLimit, setPlansLowerLimit] = useState(notificationConfigs?.lowerLimit)
	const [unsubscribeAll, setUnsubscribeAll] = useState(hasSubscription ? notificationConfigs?.unsubscribeAll : true)
	const handleChangePlansLowerLimit = event => {
		if (event.target.value < 0) return setPlansLowerLimit(0)
		if (event.target.value > 5) return setPlansLowerLimit(5)
		setPlansLowerLimit(event.target.value)
	}
	const updatePlansLowerLimit = async () => {
		await Meteor.callAsync('notificationsConfigs.changePlansLimit', {
			userId: Meteor.userId(),
			lowerLimit: Number(plansLowerLimit),
		})
	}
	const toggleUnsubscribeAll = async () => {
		const value = !unsubscribeAll
		setUnsubscribeAll(value)
		await Meteor.callAsync('notificationsConfigs.toggleUnsubscribeAll', {
			userId: Meteor.userId(),
			value,
		})
	}
	if (!notificationConfigs) return null
	return (
		<TableContainer
			component={Paper}
			sx={{ width: '100%', boxShadow: 'none' }}>
			<Table>
				<TableHead>
					<TableRow>
						<TableCell>
							<Typography variant="subtitle-sm">Send me a notification:</Typography>
						</TableCell>
						<TableCell align="center">
							<Typography variant="body">In-App</Typography>
						</TableCell>
						<TableCell align="center">
							<Typography variant="body">Push</Typography>
						</TableCell>
					</TableRow>
				</TableHead>
				<TableBody>
					<NotificationsConfigsRow
						label="When a client completes a plan"
						value={notificationConfigs.planCompleted}
						option="planCompleted"
						unsubscribeAll={unsubscribeAll}
					/>
					<NotificationsConfigsRow
						label="When a client skips a plan"
						value={notificationConfigs.planSkipped}
						option="planSkipped"
						unsubscribeAll={unsubscribeAll}
					/>
					<NotificationsConfigsRow
						label="When a client completes a survey"
						value={notificationConfigs.clientCompletedSurvey}
						option="clientCompletedSurvey"
						unsubscribeAll={unsubscribeAll}
					/>
					<NotificationsConfigsRow
						label="When a client sends feedback"
						value={notificationConfigs.clientSendFeedback}
						option="clientSendFeedback"
						unsubscribeAll={unsubscribeAll}
					/>
					<NotificationsConfigsRow
						label={
							<>
								When a client has less than
								<span>
									<InputBase
										component={'input'}
										value={plansLowerLimit}
										onChange={handleChangePlansLowerLimit}
										onBlur={updatePlansLowerLimit}
										sx={{
											width: '40px',
											backgroundColor: 'var(--primary-50)',
											border: '1px solid var(--primary-300)',
											borderRadius: '4px',
											margin: '0 8px',
											'& input': {
												textAlign: 'center',
											},
										}}
									/>
								</span>
								plans left
							</>
						}
						value={notificationConfigs.lowerLimitReached}
						option="lowerLimitReached"
						unsubscribeAll={unsubscribeAll}
					/>
				</TableBody>
				<ToggleButton
					active={unsubscribeAll}
					onChecked={toggleUnsubscribeAll}>
					<Typography variant="body">Unsubscribe me from all notifications</Typography>
				</ToggleButton>
			</Table>
		</TableContainer>
	)
}

const ToggleButtonContainer = styled(TableRow)({
	display: 'flex',
	alignItems: 'center',
	cursor: 'pointer',
	border: 'none',
})

const ToggleButton = ({ children, active, onChecked }) => {
	const { hasSubscription } = useSubscription()
	const [checked, setChecked] = useState(false)
	useEffect(() => {
		setChecked(active)
	}, [active])
	const handleCheck = event => {
		if (!hasSubscription) {
			return openModal({
				modal: StripeSubscriptionsModal,
			})
		}
		setChecked(event.target.checked)
		onChecked(event.target.checked)
	}
	return (
		<TableFooter>
			<ToggleButtonContainer>
				<TableCell
					colSpan={3}
					sx={{
						border: 'none',
					}}>
					<Stack
						direction="row"
						sx={{
							alignItems: 'center',
							gap: 1.5,
						}}>
						<Switch
							checked={checked}
							color="primary"
							onChange={handleCheck}
						/>
						{children}
					</Stack>
				</TableCell>
			</ToggleButtonContainer>
		</TableFooter>
	)
}
