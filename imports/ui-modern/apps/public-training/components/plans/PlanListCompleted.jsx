import { PlansList } from './PlansList'
import React from 'react'
import { usePlansPublic } from '@pages/client/hooks/usePlansPublic'
import { useQueryParamsPagination } from '@ui-common/hooks/state/useQueryParamsPagination'
import { useClientSession } from '@app-client/hooks/useClientSession'

export function PlanListCompleted() {
	const { client } = useClientSession()
	const { page, limit } = useQueryParamsPagination({ limit: 6 })
	const { data: completedPlans } = usePlansPublic({
		filters: { clientId: client?._id, status: 'done' },
		options: { skip: page * limit, limit, sort: { statusCompleteDate: -1 } },
	})
	return (
		<PlansList
			title={'Previous Plans'}
			plans={completedPlans}
			paginate
		/>
	)
}
