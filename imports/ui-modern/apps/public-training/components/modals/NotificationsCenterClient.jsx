import React, { useEffect } from 'react'
import { ModalContent } from '@ui-libs/modal-portal/default-variants/ModalContent'
import { openModal } from '@ui-libs/modal-portal/ModalPortal'
import { useNotificationsCount } from '@lib/QuickWebApp/Notifications/client/hooks/useNotificationsCount'
import Box from '@mui/material/Box'
import { Badge } from '@mui/material'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faBell } from '@fortawesome/pro-regular-svg-icons'
import { useQueryClient } from '@tanstack/react-query'
import { MethodCall } from '@lib/method/method'
import { capture } from '@lib/tryit/tryit'
import { SubscribeBanner } from '@ui-common/components/notifications/SubscribeBanner'
import { NotificationsCenter } from '@ui-common/components/notifications/NotificationsCenter'
import MenuItem from '@mui/material/MenuItem'
import Switch from '@mui/material/Switch'
import Divider from '@mui/material/Divider'
import { useWebPush } from '@app-nexus/hooks/useWebPush'
import { useSearchParams } from 'react-router-dom'
import { useClientSession } from '@app-client/hooks/useClientSession'
import Stack from '@mui/material/Stack'
import Typography from '@mui/material/Typography'

function NotificationsCenterClient({ userId, slug, call }) {
	const queryClient = useQueryClient()
	const { subscribe, unsubscribe, isSubscribed } = useWebPush({
		scope: `/pt/${slug}/`,
		userId,
	})
	const { coach, isPro } = useClientSession()
	console.log('coach', coach)
	const markAllAsRead = async () => {
		await queryClient.cancelQueries({
			queryKey: ['notifications', 'notificationsCount'],
		})
		const [error] = await MethodCall('notification.markAllAsRead', { userId })
		if (error) {
			capture(error)
		}
		await queryClient.invalidateQueries({ queryKey: ['notifications', 'notificationsCount'] })
	}
	const markAllAsUnread = async () => {
		await queryClient.cancelQueries({ queryKey: ['notifications', 'notificationsCount'] })
		const [error] = await MethodCall('notification.markAllAsUnread', { userId })
		if (error) {
			capture(error)
		}
		await queryClient.invalidateQueries({ queryKey: ['notifications', 'notificationsCount'] })
	}
	return (
		<ModalContent
			title={'Notifications Center'}
			slots={{
				footer: isPro && (
					<SubscribeBanner
						scope={`/pt/${slug}/`}
						userId={userId}
					/>
				),
			}}
			sx={{
				py: 0,
			}}
			menu={[
				<MenuItem
					key="mark-all-as-read"
					onClick={markAllAsRead}>
					Mark all as read
				</MenuItem>,
				<MenuItem
					key="mark-all-as-unread"
					onClick={markAllAsUnread}>
					Mark all as unread
				</MenuItem>,
				<Divider key="divider" />,
				<MenuItem
					key="push-notifications"
					onClick={async () => {
						if (isSubscribed) {
							await unsubscribe()
						} else if (isPro) {
							await subscribe()
						}
					}}>
					Push Notifications
					<Switch
						sx={{
							ml: 1,
						}}
						disabled={!isPro}
						checked={isSubscribed}
						color="primary"
						inputProps={{ 'aria-label': 'controlled' }}
						size="small"
					/>
				</MenuItem>,
			]}
			call={call}>
			{isPro ? (
				<NotificationsCenter userId={userId} />
			) : (
				<Stack
					sx={{
						width: 1,
						height: '400px',
						justifyContent: 'center',
						alignItems: 'center',
					}}>
					<Typography
						variant="text-lg"
						fontWeight={500}>
						Yay! Your inbox is empty.
					</Typography>
				</Stack>
			)}
		</ModalContent>
	)
}

export function NotificationsCenterClientTrigger({ slug, client }) {
	const [searchParams] = useSearchParams()
	const notifications = searchParams.get('notifications')

	const { data: pendingNotificationsCount } = useNotificationsCount({ userId: client?._id, read: false })
	useEffect(() => {
		if (Meteor?.userId() && Meteor.userId() !== client._id) return
		if (!navigator?.setAppBadge || !navigator?.clearAppBadge) return
		if (pendingNotificationsCount && pendingNotificationsCount > 0) {
			navigator?.setAppBadge(Number(pendingNotificationsCount))
		} else {
			navigator?.clearAppBadge()
		}
	}, [pendingNotificationsCount])

	const openNotifications = async () => {
		await openModal({
			modal: NotificationsCenterClient,
			props: {
				userId: client?._id,
				slug,
			},
		})
	}

	useEffect(() => {
		if (notifications) {
			openNotifications()
		}
	}, [notifications])
	return (
		<Box
			onClick={openNotifications}
			sx={{
				width: '30px',
				height: '30px',
				borderRadius: '5px',
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				'&: hover': {
					cursor: 'pointer',
					backgroundColor: 'rgba(200,200,200,0.3)',
				},
			}}>
			<Badge
				badgeContent={Number(pendingNotificationsCount)}
				max={99}
				sx={{
					color: 'primary.contrastTextDark',
					'& .MuiBadge-badge': {
						backgroundColor: 'primary.contrastTextLight',
						color: 'primary.contrastTextDark',
					},
				}}>
				<FontAwesomeIcon
					style={{ color: 'inherit' }}
					icon={faBell}
					size={'lg'}
				/>
			</Badge>
		</Box>
	)
}
