import { createTheme } from '@mui/material/styles'
import chroma from 'chroma-js'
import { generateAPCAColorPalette } from './color-palette.helper'

const gray = generateAPCAColorPalette('#b6b6b6')

const typography = {
	allVariants: {
		fontFamily: "'Inter', sans-serif",
		color: 'palette.primary.text',
	},
	title: {
		fontSize: 'clamp(1.5rem, 0.233vw + 1.439rem, 1.625rem)', // 24px - 26px
		color: gray[800],
		fontWeight: 600,
		letterSpacing: '0.03em',
	},
	'subtitle-lg': {
		fontSize: 'clamp(1.375rem, 0.233vw + 1.314rem, 1.5rem)', // 22px - 24px
		fontWeight: 600,
		letterSpacing: '0.02em',
		color: gray[800],
	},
	subtitle: {
		fontSize: 'clamp(1.25rem, 0.233vw + 1.189rem, 1.375rem)', // 20px - 22px
		fontWeight: 600,
		letterSpacing: '0.01em',
		color: gray[800],
	},
	'subtitle-sm': {
		fontSize: 'clamp(1.125rem, 0.233vw + 1.064rem, 1.25rem)', // 18px - 20px
		fontWeight: 600,
		color: gray[800],
	},
	'subtitle-xs': {
		fontSize: 'clamp(1rem, 0.233vw + 0.939rem, 1.125rem)', // 16px - 18px
		fontWeight: 600,
		color: gray[800],
	},
	'body-lg': {
		fontSize: 'clamp(1rem, 0.233vw + 0.939rem, 1.125rem)', // 16px - 18px
		fontWeight: 400,
		color: gray[900],
	},
	body: {
		fontSize: 'clamp(0.875rem, 0.233vw + 0.814rem, 1rem)', // 14px - 16px
		fontWeight: 400,
		color: gray[900],
	},
	'body-sm': {
		fontSize: 'clamp(0.75rem, 0.233vw + 0.689rem, 0.875rem)', // 12px - 14px
		fontWeight: 400,
		color: gray[900],
	},
	'link-lg': {
		fontSize: 'clamp(1rem, 0.233vw + 0.939rem, 1.125rem)', // 16px - 18px
		fontWeight: 400,
		textDecoration: 'underline',
		cursor: 'pointer',
		color: gray[900],
	},
	link: {
		fontSize: 'clamp(0.875rem, 0.233vw + 0.814rem, 1rem)', // 14px - 16px
		fontWeight: 400,
		textDecoration: 'underline',
		cursor: 'pointer',
		color: gray[900],
	},
	'link-sm': {
		fontSize: 'clamp(0.75rem, 0.233vw + 0.689rem, 0.875rem)', // 12px - 14px
		fontWeight: 400,
		textDecoration: 'underline',
		cursor: 'pointer',
		color: gray[900],
	},
	caption: {
		fontSize: 'clamp(0.688rem, 0.233vw + 0.626rem, 0.813rem)', // 11px - 13px
		fontWeight: 400,
		color: gray[700],
	},
	label: {
		fontSize: 'clamp(0.688rem, 0.233vw + 0.626rem, 0.813rem)', // 11px - 13px
		fontWeight: 500,
		color: gray[700],
		lineHeight: 2,
	},
}

export function generateCustomBrandTheme(branding, _mode) {
	const { themeColor, backgroundColor } = branding || { themeColor: '#033598', backgroundColor: '#ffffff' }
	const primary = generateAPCAColorPalette(themeColor)
	const secondary = generateAPCAColorPalette(backgroundColor)
	const alert = generateAPCAColorPalette('#ffc03c')
	const error = generateAPCAColorPalette('#c20000')
	return createTheme({
		palette: {
			brand: {
				theme: themeColor,
				background: backgroundColor,
			},
			primary,
			secondary,
			alert,
			error,
			gray,
			background: {
				notification: chroma.mix(primary[50], '#fff', 0.9).hex(),
				notificationGray: chroma.mix(gray[50], '#fff', 0.9).hex(),
				card: chroma.mix(primary[50], '#fff', 0.8).hex(),
				cardGray: chroma.mix(gray[50], '#fff', 0.6).hex(),
				paper: chroma.mix(themeColor, '#fff', 0.96).hex(),
				light: chroma.mix(themeColor, '#fff', 0.98).hex(),
			},
		},
		typography,
		components: {
			MuiCard: {
				styleOverrides: {
					root: {
						padding: '16px',
						boxShadow: 'none',
						border: '1px solid',
						borderColor: gray[50],
						borderRadius: '12px',
					},
				},
			},
		},
	})
}
