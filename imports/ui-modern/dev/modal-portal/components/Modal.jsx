import React from 'react'
import Box from '@mui/material/Box'
import { useModalStyles } from './Modal.styles'

/**
 * @deprecated use new Modal components instead
 */
export function Modal({ position = 'center', variant = 'custom', margin = 2, sx = {}, ...props }) {
	const style = useModalStyles({ position, margin, sx })
	const variantsMapping = {
		custom: ModalCustom,
		steps: StepsModal,
		fullscreen: ModalFullScreen,
	}
	return variantsMapping[variant]({ sx: style, ...props })
}

function StepsModal({}) {}

function ModalCustom({ sx, children, header, body, footer, call, ...props }) {
	return (
		<Box
			sx={[
				...(Array.isArray(sx) ? sx : [sx]),
				{
					p: 2,
					width: {
						xs: '94%',
						sm: '380px',
					},
				},
			]}>
			{header}
			{body}
			{children}
			{footer}
		</Box>
	)
}

function ModalFullScreen({ sx, children, call, ...props }) {
	return (
		<Box
			sx={[
				...(Array.isArray(sx) ? sx : [sx]),
				{
					borderRadius: 0,
					backgroundColor: 'black',
					p: 4,
					width: {
						xs: '100%',
					},
					height: {
						xs: '100%',
					},
				},
			]}>
			{children(call)}
		</Box>
	)
}
