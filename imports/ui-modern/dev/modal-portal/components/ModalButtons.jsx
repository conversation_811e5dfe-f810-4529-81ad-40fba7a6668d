import React from 'react'
import Stack from '@mui/material/Stack'
import { ButtonContained, ButtonOutlined, ButtonText } from '@ui-common/components/forms'

export const ModalButtons = ({ primary, secondary, tertiary }) => (
	<Stack
		sx={{
			gap: 1,
			pt: 2,
			flexDirection: 'column',
		}}>
		{(primary || secondary) && (
			<Stack
				sx={{
					gap: 1,
					width: '100%',
					flexDirection: {
						xs: 'column-reverse',
						sm: 'row',
					},
				}}>
				{secondary && (
					<ButtonOutlined
						sx={{
							flex: primary ? 1 : '1 1 100%',
						}}
						color={secondary.destructive ? 'error' : 'gray'}
						{...secondary}>
						{secondary.text}
					</ButtonOutlined>
				)}
				{primary && (
					<ButtonContained
						sx={{
							flex: secondary ? 1 : '1 1 100%',
						}}
						color={primary.destructive ? 'error' : 'primary'}
						{...primary}>
						{primary.text}
					</ButtonContained>
				)}
			</Stack>
		)}
		{tertiary && (
			<Stack>
				<ButtonText {...tertiary}>{tertiary.text}</ButtonText>
			</Stack>
		)}
	</Stack>
)
