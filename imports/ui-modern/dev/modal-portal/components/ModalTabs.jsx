import React from 'react'
import { Tab, Tabs as MuiTabs } from '@mui/material'

export function ModalTabs({ tab, setTab, tabs, onChangeTab, disable }) {
	const handleChange = (event, newValue) => {
		const newTab = tabs.find(tab => tab.value === newValue)
		setTab(newTab)
		onChangeTab?.(newTab)
	}
	return (
		<MuiTabs
			variant="fullWidth"
			value={tab.value}
			disable={String(disable)}
			onChange={handleChange}>
			{tabs.map((tab, index) => (
				<Tab
					key={index}
					value={tab.value}
					label={tab.label}
					sx={{
						textTransform: 'capitalize',
						boxShadow: 'none',
						bgcolor: 'white',
						color: 'gray.700',
						fontSize: '14px',
						'&:hover': {
							backgroundColor: 'primary.50',
							boxShadow: 'none',
							color: 'primary.800',
						},
						'&.Mui-selected': {
							color: 'primary.700',
						},
						'&:last-child': {
							borderLeft: 'none',
						},
						'&:first-child': {
							borderRight: 'none',
						},
					}}
				/>
			))}
		</MuiTabs>
	)
}
