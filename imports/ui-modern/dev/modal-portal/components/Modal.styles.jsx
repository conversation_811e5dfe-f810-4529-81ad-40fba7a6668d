/**
 * Generates styles for a modal component based on the provided position, margin, and additional styles.
 *
 * @param {Object} options - The options for generating modal styles.
 * @param {object|string} options.position - The position of the modal. Can be a string or an object with breakpoints.
 * @param {number} options.margin - The margin around the modal.
 * @param {Object} options.sx - Additional styles to be applied to the modal.
 * @returns {Array} An array containing the default styles, position styles, and additional styles.
 * @throws {Error} If the position object is empty or not an object.
 */
export const useModalStyles = ({ position = { xs: 'bottom', sm: 'center' }, margin = 2, sx = {} }) => {
	const defaultStyles = {
		position: 'absolute',
		backgroundColor: 'white',
		borderRadius: 3,
		overflow: 'hidden',
		width: {
			xs: '96%',
			sm: '480px',
		},
	}

	const positionStyles = {
		center: {
			top: '50%',
			left: '50%',
			bottom: 'auto',
			right: 'auto',
			transform: 'translate(-50%, -50%)',
		},
		top: {
			top: margin * 8,
			left: '50%',
			bottom: 'auto',
			right: 'auto',
			transform: 'translate(-50%, 0)',
		},
		bottom: {
			bottom: margin * 8,
			left: '50%',
			top: 'auto',
			right: 'auto',
			transform: 'translate(-50%, 0)',
		},
		left: {
			left: margin * 8,
			top: '50%',
			bottom: 'auto',
			right: 'auto',
			transform: 'translate(0, -50%)',
		},
		right: {
			right: margin * 8,
			top: '50%',
			bottom: 'auto',
			left: 'auto',
			transform: 'translate(0, -50%)',
		},
		'top-left': {
			top: margin * 8,
			left: margin * 8,
			bottom: 'auto',
			right: 'auto',
		},
		'top-right': {
			top: margin * 8,
			right: margin * 8,
			bottom: 'auto',
			left: 'auto',
		},
		'bottom-left': {
			bottom: margin * 8,
			left: margin * 8,
			top: 'auto',
			right: 'auto',
		},
		'bottom-right': {
			bottom: margin * 8,
			right: margin * 8,
			top: 'auto',
			left: 'auto',
		},
	}

	// Handle responsive positioning
	if (typeof position === 'object') {
		// Check if position is an object with breakpoints
		if (!Object.keys(position).length) {
			throw new Error('Position object cannot be empty')
		}
		if (typeof position !== 'object') {
			throw new Error('Position must be an object with breakpoints')
		}
		const responsiveStyles = {
			top: {},
			bottom: {},
			left: {},
			right: {},
			transform: {},
		}

		// Process each breakpoint
		Object.entries(responsiveStyles).forEach(([cssKey, breakpoints]) => {
			Object.entries(position).forEach(([breakpoint, value]) => {
				const cssValue = positionStyles[value][cssKey]
				if (cssValue) {
					breakpoints[breakpoint] = cssValue
				}
			})
		})

		console.log(responsiveStyles)

		return [defaultStyles, responsiveStyles, sx]
	}

	// Default behavior for string position
	return [defaultStyles, positionStyles[position], sx]
}
