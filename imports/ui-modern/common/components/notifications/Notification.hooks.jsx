import { MethodCall } from '@lib/method/method'
import { useQueryClient } from '@tanstack/react-query'
import { useEffect } from 'react'
import { Seconds } from '../../../../shared/common/helpers/time.helpers'

export function useNotification(notificationId, seen) {
	const queryClient = useQueryClient()
	const cancelQueries = async () => {
		await queryClient.cancelQueries({ queryKey: ['notifications', 'notificationsCount'] })
	}
	const invalidateQueries = async () => {
		await queryClient.invalidateQueries({ queryKey: ['notifications', 'notificationsCount'] })
	}
	const markAsSeen = async () => {
		await cancelQueries()
		await MethodCall('notification.markAsSeen', { _id: notificationId })
		await invalidateQueries()
	}
	const markAsRead = async event => {
		event?.stopPropagation()
		event?.nativeEvent?.stopImmediatePropagation()
		await cancelQueries()
		await MethodCall('notification.markAsRead', { _id: notificationId })
		await invalidateQueries()
	}
	const markAsUnread = async event => {
		event?.stopPropagation()
		event?.nativeEvent?.stopImmediatePropagation()
		await cancelQueries()
		await MethodCall('notification.markAsUnread', { _id: notificationId })
		await invalidateQueries()
	}
	useEffect(() => {
		if (seen) {
			return
		}
		const timer = setTimeout(markAsSeen, Seconds(2))
		return () => clearTimeout(timer)
	}, [seen, markAsSeen])
	return {
		markAsSeen,
		markAsRead,
		markAsUnread,
	}
}
