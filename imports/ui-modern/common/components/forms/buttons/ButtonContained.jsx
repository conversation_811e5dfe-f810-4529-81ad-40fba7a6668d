import { styled } from '@mui/material/styles'
import { Button } from '@mui/material'

export const ButtonContained = styled(Button)(({ theme, color = 'primary', width = '100%' }) => ({
	width,
	backgroundColor: theme.palette[color].main,
	border: `1px solid ${theme.palette[color].main}`,
	color: theme.palette[color].contrastTextDark,
	borderRadius: '8px',
	boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
	fontWeight: 600,
	fontSize: '0.9rem',
	height: '44px',
	lineHeight: '24px',
	padding: '10px 18px',
	textTransform: 'none',
	'&:hover': {
		backgroundColor: theme.palette[color].dark,
		color: '#FFFFFF',
	},
	'&:active': {
		boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
	},
	'&:focus': {
		boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
	},
	'&:disabled': {
		color: '#fff',
		opacity: '0.6',
	},
}))

export const ButtonContainedSmall = styled(Button)(({ theme, color = 'primary', width = '100%' }) => ({
	width,
	backgroundColor: theme.palette[color].main,
	color: theme.palette[color].contrastTextDark,
	border: `1px solid ${theme.palette[color].main}`,
	borderRadius: '16px',
	boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
	fontWeight: 600,
	fontSize: '0.8rem',
	height: '32px',
	lineHeight: '24px',
	padding: '4px 24px',
	textTransform: 'none',
	'&:hover': {
		color: '#FFFFFF',
		backgroundColor: theme.palette[color].dark,
		border: `1px solid ${theme.palette[color].dark}`,
	},
	'&:active': {
		boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
	},
	'&:focus': {
		boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
	},
	'&:disabled': {
		color: '#fff',
		opacity: '0.6',
	},
}))
