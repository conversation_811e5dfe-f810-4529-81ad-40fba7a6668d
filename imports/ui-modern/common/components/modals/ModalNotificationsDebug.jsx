import React, { useEffect, useState } from 'react'
import Modal from '@ui-libs/modal-portal/Modal'
import { registerCoachAppServiceWorker } from '@app-nexus/helpers/service-worker.helper'
import { checkNotificationsPermission, checkScopedNotificationsPermission } from '@app-nexus/helpers/permissions.helper'
import { getPushManagerSubscription, getPushManagerSubscriptionToken } from '@app-nexus/helpers/push-manager.helper'
import { getDeviceDetails } from '@app-nexus/helpers/device.helper'

export function ModalNotificationsDebug({ onClose, call }) {
	const [registration, setRegistration] = useState(null)
	const [globalPermission, setGlobalPermission] = useState('prompt')
	const [scopedPermission, setScopedPermission] = useState('prompt')
	const [subscription, setSubscription] = useState(null)
	const [token, setToken] = useState(null)
	const [device, setDevice] = useState(null)

	useEffect(() => {
		const init = async () => {
			try {
				const _registration = await registerCoachAppServiceWorker()
				const _globalPermission = await checkNotificationsPermission()
				const _scopedPermission = await checkScopedNotificationsPermission('/app/')
				const _subscription = await getPushManagerSubscription(_registration)
				const _token = await getPushManagerSubscriptionToken('/app/')
				const _device = await getDeviceDetails()

				console.log('_registration', _registration)
				setRegistration({
					scope: _registration?.scope,
					scriptURL: _registration?.active ? _registration?.active?.scriptURL : 'not active',
					state: _registration?.active ? registration?.active?.state : 'not active',
				})
				setGlobalPermission(_globalPermission)
				setScopedPermission(_scopedPermission)
				setSubscription(_subscription)
				setToken(_token)
				setDevice({
					browser: _device?.browser,
					os: _device?.os,
					device: _device?.device,
				})
			} catch (error) {
				console.error('Error recovering web push state:', error)
			}
		}
		init()
	}, [])

	console.log('ModalNotificationsDebug', {
		registration,
		globalPermission,
		scopedPermission,
		subscription,
		token,
		device,
	})

	return (
		<Modal>
			<Modal.Header
				title={'Notifications Debug'}
				onClose={onClose}
			/>
			<Modal.Body>
				<pre
					style={{
						overflow: 'auto',
						height: '75dvh',
					}}>
					{JSON.stringify(
						{ registration, globalPermission, scopedPermission, subscription, token, device },
						null,
						2,
					)}
				</pre>
			</Modal.Body>
			<Modal.Actions
				secondary={{
					text: 'Close',
					onClick: onClose,
				}}
			/>
		</Modal>
	)
}
