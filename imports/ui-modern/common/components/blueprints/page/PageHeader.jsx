import Grid from '@mui/material/Grid2'
import { PageBreadcrumbs } from '@ui-common/components/blueprints/page/PageBreadcrumbs'
import DocumentationLink from '../../../../../ui/components/doc/DocumentationLink'
import Avatar from '@mui/material/Avatar'
import { Typography } from '@mui/material'
import { LightGrayParagraph } from '../../../../../ui/utility/labels/CustomLabels'
import React from 'react'

export function PageHeader({ title, description, avatar, breadcrumbs, titleMeta, hiddenTitle = false, docs }) {
	return (
		<Grid
			container
			spacing={2}>
			<Grid
				size={10}
				sx={{ display: 'flex', alignItems: 'center' }}>
				<PageBreadcrumbs breadcrumbs={breadcrumbs} />
			</Grid>
			<Grid
				size={2}
				sx={{ display: 'flex', alignItems: 'center', justifyContent: 'flex-end', gap: 2 }}>
				{titleMeta}
				<DocumentationLink docs={docs} />
			</Grid>
			<Grid
				size={12}
				sx={{ display: 'flex', alignItems: 'center', gap: 2 }}>
				{avatar && (
					<Avatar
						alt={avatar?.label}
						src={avatar?.src}
						sx={{
							width: 64,
							height: 64,
							bgcolor: 'primary.50',
							color: 'primary.600',
							fontWeight: 500,
							fontSize: '24px',
							lineHeight: '32px',
						}}>
						{avatar?.label}
					</Avatar>
				)}
				{!hiddenTitle && (
					<Typography
						variant="title"
						sx={{
							fontSize: '30px',
							fontWeight: 'bold',
							color: 'primary.950',
						}}>
						{title}
					</Typography>
				)}
			</Grid>
			<Grid size={12}>
				{typeof description === 'string' ? <LightGrayParagraph>{description}</LightGrayParagraph> : description}
			</Grid>
		</Grid>
	)
}
