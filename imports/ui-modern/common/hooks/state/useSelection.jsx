import { useState } from 'react'

export const useSelection = ({ initialSelection = [], onSelect = _ => {}, onClear = _ => {}, items, batch }) => {
	const [selectedItems, setSelectedItems] = useState(initialSelection)
	const isItemSelected = item => selectedItems.includes(item)
	const selectItem = item => {
		if (!item) {
			return console.warn('Item is undefined')
		}
		if (typeof item !== 'string' && typeof item !== 'number') {
			return console.warn('Item must be a string or number')
		}
		const selected = isItemSelected(item)
		setSelectedItems(selected ? selectedItems.filter(selected => selected !== item) : [...selectedItems, item])
		onSelect(selectedItems)
	}
	const selectAll = () => {
		if (batch) {
			const batchItems = selectedItems.concat(items.filter(item => !selectedItems.includes(item)))
			setSelectedItems(batchItems)
			onSelect(batchItems)
		} else {
			setSelectedItems(items)
			onSelect(items)
		}
	}
	const clearSelection = () => {
		setSelectedItems([])
		onClear()
	}
	return {
		// state
		selectedItems,
		setSelectedItems,
		// methods
		selectItem,
		selectAll,
		clearSelection,
		// computed
		isItemSelected,
	}
}
