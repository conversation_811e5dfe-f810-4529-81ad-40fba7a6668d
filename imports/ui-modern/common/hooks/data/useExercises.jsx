import { getExercises } from '@collections/exercises/exercises_Q'
import { useGrapherCounter } from '@ui-libs/create-query/useGrapherCounter'
import { useGrapherQuery } from '@ui-libs/create-query/useGrapherQuery'

export const useExercises = ({ params, single, debug, transform }, settings = {}) => {
	return useGrapherQuery({
		query: getExercises,
		params,
		single,
		debug,
		transform,
	})
}

export const useExercisesCount = filters => {
	return useGrapherCounter({ query: getExercises, filters })
}
