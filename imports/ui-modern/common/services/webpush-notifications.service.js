import { toast } from 'react-toastify'
import { MethodCall } from '@lib/method/method'

export const sendNotification = async (userId, payload) => {
	if (!payload) return toast.error('Notification not sent: payload is empty')
	const [saveError, notificationId] = await MethodCall('notification.save', { userId, ...payload })
	if (saveError) return saveError.notify()
	payload.notification.tag = `Notification-${notificationId}`
	console.log('payload', payload)
	const [error] = await MethodCall('notification.send', { _id: notificationId, userId, ...payload })
	console.log('error', error)
	if (error) return error.notify()
}

export const sendNotificationMultiTarget = async (userIds, payload) => {
	if (!payload) return toast.error('Notification not sent: payload is empty')
	for (const userId of userIds) {
		if (!payload.data?.url) {
			const client = await Meteor.callAsync('coachClients.findOne', {
				selector: { clientId: userId },
				options: { fields: { slug: 1 } },
			})
			console.log('client', client)
			if (client?.slug) {
				payload.data.url = Meteor.absoluteUrl(`pt/${client.slug}?notifications=true`)
			}
		}
		await sendNotification(userId, payload)
	}
}
