import React from 'react'
import MuiPhoneNumber from 'dyxbenjamin-mui-phone-number'
import { Meteor } from 'meteor/meteor'

const style = {
	'&.MuiFormControl-root': {
		background: 'white',
		border: '1px solid var(--gray-300) !important',
		boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
		borderRadius: '8px',
		color: 'var(--gray-500)',
		fontSize: '16px',
		fontWeight: 'normal',
		lineHeight: '24px',
		margin: 0,
		width: '100%',
		height: '44px',
		transition: 'all 200ms ease',
		display: 'flex',
		alignItems: 'flex-start',
		justifyContent: 'center',
	},
	'& .MuiInputBase-root': {
		width: '100%',
	},
	'& .MuiInputBase-input': {
		width: '100%',
	},
	'& .MuiOutlinedInput-multiline': {
		padding: '0 !important',
	},
	'& .MuiInputLabel-root': {
		display: 'none',
	},
	'&.Mui-focused': {
		border: 'none !important',
	},
	'&:focus': {
		outline: 'none',
	},
	'&:active': {
		outline: 'none',
	},
	'& input::placeholder': {
		color: 'var(--gray-500)',
	},
	'& input': {
		width: '100%',
		padding: 0,
	},
	'& textarea': {
		padding: 0,
	},
	'& fieldset': {
		border: 'none',
	},
}

export default function PhoneInput(props) {
	return (
		<MuiPhoneNumber
			sx={style}
			variant={'outlined'}
			defaultCountry="us"
			disableDropdown={Meteor.isServer}
			name="phone"
			disableAreaCodes
			{...props}
		/>
	)
}
