import React, { useState } from 'react'
import { InputText } from '@design/components/forms/Inputs/CustomTextField'
import MenuItem from '@mui/material/MenuItem'
import Autocomplete from '@mui/material/Autocomplete'

const DefaultOptionSlot = ({ option }) => {
	return <MenuItem>{option?.text || option?.name || option?.title}</MenuItem>
}

/**
 * ComboBox
 *
 * A customizable combo box component with autocomplete functionality.
 *
 * @param {ReadonlyArray} options - The list of options for the autocomplete. Can be an array or a function returning an array.
 * @param {Object} slots - Custom slots for rendering options. Defaults to using `DefaultOptionSlot`.
 * @param {any} [defaultValue] - The initial value of the combo box.
 * @param {Function} [onChange] - Callback function triggered when the value changes.
 * @param {string} [placeholder] - Placeholder text for the input field.
 * @param {string} [field] - The field name to access properties of options if they are objects.
 *
 * @throws {Error} If options are objects and no `field` is provided.
 *
 * @returns {JSX.Element} The ComboBox component.
 */
export default function ComboBox({
	options = [],
	slots = { option: DefaultOptionSlot },
	defaultValue,
	onChange,
	placeholder,
	labelField,
}) {
	if (typeof options[0] === 'object' && !labelField) {
		throw new Error('[ComboBox] You must provide a field name to access the object')
	}
	return (
		<Autocomplete
			disablePortal
			options={options}
			sx={{ width: 1 }}
			placeholder={placeholder}
			defaultValue={defaultValue}
			getOptionLabel={option => {
				if (labelField) {
					return option?.[labelField]
				}
				return option
			}}
			renderInput={params => <InputText {...params} />}
		/>
	)
}
