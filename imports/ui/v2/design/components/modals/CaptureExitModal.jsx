import React from 'react'
import { <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>ead<PERSON> } from '@design/layouts'
import { ButtonContained, ButtonOutlined } from '@ui-common/components/forms'

export default function CaptureExitModal({ onClose, call }) {
	return (
		<>
			<ModalHeader
				title={'You have unsaved changes'}
				subtitle={'Are you sure you want to leave? \n All your changes will be lost.'}
				handleClose={onClose}
			/>
			<ModalFooter>
				<ButtonOutlined onClick={onClose}>No, take me back</ButtonOutlined>
				<ButtonContained
					onClick={() => {
						call.end(true)
					}}>
					Yes, leave
				</ButtonContained>
			</ModalFooter>
		</>
	)
}
