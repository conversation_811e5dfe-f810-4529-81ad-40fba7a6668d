import React from 'react'
import { TablePagination, tablePaginationClasses } from '@mui/material'
import { useDataTableContext } from '@design/components/data/DataTable/context/DataTableContext'

export default function DataTablePagination() {
	const { pagination } = useDataTableContext()

	if (!pagination) return null
	if (pagination.count <= 0) return null

	const onPageChange = (_, page) => {
		pagination.setPage(page)
	}

	const setRowsPerPage = event => {
		pagination.setLimit(event.target.value)
	}

	return (
		<TablePagination
			component="div"
			count={pagination.count}
			page={pagination.page}
			onPageChange={onPageChange}
			rowsPerPage={pagination.limit}
			rowsPerPageOptions={pagination?.options ?? [10, 20, 40]}
			onRowsPerPageChange={setRowsPerPage}
			showFirstButton
			showLastButton
			sx={{
				width: '100%',
				borderBottom: 'none',
				fontSize: '0.9rem',
				'& .MuiTablePagination-spacer': {
					flex: '1 1 0',
				},
				'& .MuiTablePagination-select': {
					minWidth: '100px',
				},
				[`& .${tablePaginationClasses.actions}`]: {
					minWidth: '42px',
					height: '42px',
					borderRadius: '14px',
					'& button': {
						color: 'var(--gray-700)',
						'&:disabled': {
							color: 'var(--gray-300)',
						},
					},
				},
				'.MuiTablePagination-selectRoot': {
					minWidth: '100px',
					margin: 'auto',
				},
				'.MuiInputBase-input': {
					padding: '8px',
					fontSize: '0.9rem',
					minWidth: '100px',
				},
			}}
		/>
	)
}
