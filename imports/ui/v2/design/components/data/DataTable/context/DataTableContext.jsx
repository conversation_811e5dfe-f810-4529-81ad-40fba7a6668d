import React, { createContext, useContext, useEffect, useState } from 'react'

export const DataTableContext = createContext(null)

export const DataTableProvider = ({ children, ...props }) => {
	const [selectedRows, setSelectedRows] = useState([])

	useEffect(() => {
		if (!props.onSelect) return
		props.onSelect(selectedRows)
	}, [selectedRows])

	const selectRow = row => {
		const selected = selectedRows.includes(row)
		setSelectedRows(selected ? [...selectedRows].filter(r => r !== row) : [...selectedRows, row])
	}

	const selectAll = () => {
		setSelectedRows(props.rows.map(r => r._id))
	}

	const clearSelection = () => {
		setSelectedRows([])
	}

	const isRowSelected = row => selectedRows.includes(row._id)

	const isSortable = props.columns.some(column => column.key === 'sort')

	return (
		<DataTableContext.Provider
			value={{
				// selection: {
				// 	selectedRows,
				// 	setSelectedRows,
				// 	isRowSelected,
				// 	selectRow,
				// 	selectAll,
				// 	clearSelection,
				// },
				isSortable,
				...props,
			}}>
			{children}
		</DataTableContext.Provider>
	)
}

export const useDataTableContext = () => useContext(DataTableContext)
