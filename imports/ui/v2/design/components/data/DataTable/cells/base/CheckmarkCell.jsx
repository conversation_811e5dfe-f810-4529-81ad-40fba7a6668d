import React from 'react'
import TableCell from '@mui/material/TableCell'
import { faCheck } from '@fortawesome/pro-solid-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'

export default function CheckmarkCell({ value = false, onChange, row }) {
	return (
		<TableCell align="center">
			{Boolean(value) && (
				<FontAwesomeIcon
					icon={faCheck}
					size="xl"
					color="var(--primary-600)"
				/>
			)}
		</TableCell>
	)
}
