import React from 'react'
import { TableCell } from '@mui/material'
import Typography from '@mui/material/Typography'

export default function NameCell({ value }) {
	return (
		<TableCell align="left">
			<Typography
				variant="body"
				component="span"
				sx={{
					margin: 'auto',
					textAlign: 'left',
					color: 'var(--gray-950)',
				}}>
				{value.firstName} {value.lastName}
			</Typography>
		</TableCell>
	)
}
