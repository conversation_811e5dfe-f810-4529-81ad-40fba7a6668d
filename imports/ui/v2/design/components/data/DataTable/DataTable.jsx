import React from 'react'
import TableContainer from '@mui/material/TableContainer'
import Paper from '@mui/material/Paper'
import DataTableHeader from './DataTableHeader'
import Table from '@mui/material/Table'
import { isEmpty } from 'lodash'
import Box from '@mui/material/Box'
import DataTableBody from './DataTableBody'
import DataTableFooter from '@design/components/data/DataTable/DataTableFooter'
import { DataTableProvider } from '@design/components/data/DataTable/context/DataTableContext'
import { DataTableLoading } from '@design/components/data/DataTable/components/DataTableLoading'

export default function DataTable({
	title,
	subtitle,
	columns,
	rows,
	embedded = false,
	emptyFallback,
	loading,
	onSelect,
	selection,
	onSort,
	pagination,
	sortable,
}) {
	if (loading && isEmpty(rows)) {
		return <DataTableLoading embedded={embedded} />
	}
	if (!loading && isEmpty(rows)) {
		return (
			<Box
				sx={{
					display: 'flex',
					flexDirection: 'column',
					alignItems: 'center',
					justifyContent: 'center',
					height: '100%',
					width: '100%',
					backgroundColor: 'var(--gray-100)',
					minHeight: embedded ? '100%' : '300px',
					borderRadius: embedded ? 0 : 2,
				}}>
				{emptyFallback}
			</Box>
		)
	}
	return (
		<Box
			sx={{
				width: '100%',
				backgroundColor: 'var(--gray-100)',
				overflow: 'hidden',
				border: '1px solid var(--gray-300)',
			}}>
			<DataTableProvider
				title={title}
				subtitle={subtitle}
				columns={columns}
				rows={rows}
				onSort={onSort}
				onSelect={onSelect}
				selection={selection}
				pagination={pagination}
				sortable={sortable}>
				<TableContainer
					component={Paper}
					sx={{
						minHeight: embedded ? '100%' : 'inherit',
						border: 'none',
						borderRadius: embedded ? 0 : 3,
						boxShadow: 'none',
					}}>
					<Table
						sx={{
							minWidth: 650,
							border: 'none',
						}}>
						<DataTableHeader />
						<DataTableBody />
					</Table>
				</TableContainer>
				<DataTableFooter
					rows={rows}
					columns={columns}
				/>
			</DataTableProvider>
		</Box>
	)
}
