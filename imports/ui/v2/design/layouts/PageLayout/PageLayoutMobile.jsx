import React from 'react'
import { Helmet } from 'react-helmet-async'
import Container from '@mui/material/Container'
import MobilePageHeader from './components/MobilePageHeader'
import Box from '@mui/material/Box'
import BrandedSignature from '../../../../pages/customer/components/BrandedSignature'
import Stack from '@mui/material/Stack'
import { isEmpty } from 'lodash'
import PullToRefresh from 'react-simple-pull-to-refresh'

export default function PageLayoutMobile({
	helmetTitle,
	title,
	maxWidth = 'lg',
	children,
	pageActions = [],
	onRefresh = () => {
		window.location.reload()
	},
	...props
}) {
	return (
		<>
			<Helmet>
				<title>{helmetTitle ?? title}</title>
			</Helmet>
			<Box
				sx={{
					width: '100%',
					height: '100%',
					minHeight: '100vh',
					backgroundColor: 'background.paper',
					position: 'relative',
					display: 'flex',
					flexDirection: 'column',
				}}>
				<MobilePageHeader
					title={title}
					maxWidth={maxWidth}
					{...props}
				/>
				<PullToRefresh onRefresh={onRefresh}>
					<Container
						maxWidth={maxWidth}
						component={'main'}
						sx={{
							flex: 1,
							width: '100%',
							height: '100%',
							display: 'flex',
							flexDirection: 'column',
							gap: 4,
							p: 2,
							py: 4,
							overflow: 'auto',
						}}>
						{children}
					</Container>
				</PullToRefresh>

				{!isEmpty(pageActions) && (
					<Box
						sx={{
							position: 'sticky',
							bottom: 0,
							left: 0,
							right: 0,
							padding: 1,
							paddingBottom: 2,
							backgroundColor: 'background.paper',
							borderColor: 'divider',
							zIndex: 1000,
							// boxShadow: '0px -1px 12px rgba(0, 0, 0, 0.1)',
						}}>
						<Container maxWidth={maxWidth}>
							<Stack spacing={1}>{pageActions}</Stack>
						</Container>
					</Box>
				)}
				<BrandedSignature />
			</Box>
		</>
	)
}
