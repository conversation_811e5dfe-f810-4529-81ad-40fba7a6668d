import React, { Fragment, useState } from 'react'
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import Stack from '@mui/material/Stack'
import CloseIcon from '@mui/icons-material/Close'
import IconButton from '@mui/material/IconButton'
import MoreVertIcon from '@mui/icons-material/MoreVert'
import { Menu } from '@mui/material'

export default function ModalHeader({ title, subtitle, handleClose, menuSlots, icon, children }) {
	const [anchorEl, setAnchorEl] = useState(null)
	const open = Boolean(anchorEl)
	const handleClick = event => {
		setAnchorEl(event.currentTarget)
	}
	const handleCloseMenu = () => {
		setAnchorEl(null)
	}

	return (
		<Box
			component={'header'}
			sx={{
				position: 'sticky',
				mb: 0.5,
				width: '100%',
			}}>
			<Stack
				sx={{
					pt: 1.5,
					pb: 1,
					paddingInline: 2,
				}}>
				<Stack
					width={1}
					direction="row"
					justifyContent="space-between">
					<Stack
						direction="row"
						alignItems="center">
						{icon && <Box sx={{ display: 'flex', alignItems: 'center', gap: '8px', mr: 1 }}>{icon}</Box>}
						<Typography variant="subtitle-sm">{title}</Typography>
					</Stack>
					<Stack
						direction="row"
						alignItems="center">
						{menuSlots && (
							<>
								<IconButton
									size={'small'}
									onClick={handleClick}>
									<MoreVertIcon
										sx={{
											color: 'gray.800',
										}}
									/>
								</IconButton>
								<Menu
									anchorEl={anchorEl}
									anchorOrigin={{
										vertical: 'bottom',
										horizontal: 'center',
									}}
									transformOrigin={{
										vertical: 'top',
										horizontal: 'center',
									}}
									open={open}
									onClose={handleCloseMenu}
									sx={{
										zIndex: 80000,
										'& .MuiMenuItem-root': {
											fontSize: '14px',
											color: 'gray.800',
										},
									}}>
									{menuSlots.map((menuSlot, idx) =>
										React.cloneElement(menuSlot, {
											key: 'menu-slot-' + idx,
										}),
									)}
								</Menu>
							</>
						)}
						{handleClose && (
							<IconButton
								size={'small'}
								onClick={handleClose}>
								<CloseIcon
									sx={{
										color: 'gray.800',
									}}
								/>
							</IconButton>
						)}
					</Stack>
				</Stack>
				<Typography
					variant="body-lg"
					sx={{ color: 'gray.500' }}>
					{subtitle}
				</Typography>
			</Stack>
			{children}
		</Box>
	)
}
