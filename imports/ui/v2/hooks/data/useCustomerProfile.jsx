import useStaticGrapher2 from '../../../hooks/useStaticGrapher2'
import { getCustomerInfo } from '@collections/users/users_Q'

export const useCustomerProfile = _id => {
	const { data, ready } = useStaticGrapher2({
		getQuery: getCustomerInfo,
		singleton: true,
		refresh: false,
		settings: {
			filters: {
				_id,
			},
		},
		debug: false,
		inputs: [_id],
	})

	if (!ready) {
		return null
	}

	return {
		_id: data?._id,
		email: data?.profile?.email,
		slug: data?.coachClients?.[0]?.slug,
		active: data?.profile?.active,
		profile: {
			fullName: data?.profile?.firstName + ' ' + data?.profile?.lastName,
			firstName: data?.profile?.firstName,
			lastName: data?.profile?.lastName,
			avatar: data?.profile?.image_url,
		},
	}
}
