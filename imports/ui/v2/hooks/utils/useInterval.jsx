import { useEffect, useRef } from 'react'

export default function useInterval(callback, delay, options = {}) {
	const { immediate = false, enabled = true } = options
	const savedCallback = useRef(callback)

	useEffect(() => {
		savedCallback.current = callback
	}, [callback])

	useEffect(() => {
		if (!enabled) return

		if (immediate) {
			savedCallback.current()
		}

		const interval = Meteor.setInterval(() => {
			savedCallback.current()
		}, delay)

		return () => Meteor.clearInterval(interval)
	}, [delay, immediate, enabled])
}
