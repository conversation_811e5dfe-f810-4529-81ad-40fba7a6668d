import { useEffect, useRef, useState } from 'react'

export const withRefresh = (config = {}) => {
	const {
		interval = null,
		enabled = true,
		onRefresh,
		refreshOnFocus = false,
		refreshOnOnline = false,
		pauseOnHidden = true,
	} = config

	return (controller, fetchData) => {
		const [isRefreshing, setIsRefreshing] = useState(false)
		const [lastRefresh, setLastRefresh] = useState(null)
		const [isPaused, setIsPaused] = useState(document.hidden)

		const intervalRef = useRef(null)
		const enabledRef = useRef(enabled)
		const isRefreshingRef = useRef(false) // Prevenir refreshes concurrentes
		const isMountedRef = useRef(true) // Prevenir actualizaciones después de desmontaje

		const clearRefreshInterval = () => {
			if (intervalRef.current) {
				clearInterval(intervalRef.current)
				intervalRef.current = null
			}
		}

		const refresh = async (silent = false) => {
			if (
				!enabledRef.current ||
				isRefreshingRef.current ||
				(pauseOnHidden && document.hidden) ||
				!isMountedRef.current
			) {
				return
			}

			try {
				isRefreshingRef.current = true
				if (!silent && isMountedRef.current) {
					setIsRefreshing(true)
				}

				await fetchData()

				if (isMountedRef.current) {
					setLastRefresh(new Date())
					onRefresh?.()
				}
			} finally {
				isRefreshingRef.current = false
				if (!silent && isMountedRef.current) {
					setIsRefreshing(false)
				}
			}
		}

		// Configurar intervalo de refresh
		const setupRefreshInterval = () => {
			clearRefreshInterval()
			if (interval && enabledRef.current && !document.hidden) {
				intervalRef.current = setInterval(() => refresh(true), interval)
			}
		}

		// Efecto para manejar visibilidad
		useEffect(() => {
			const handleVisibilityChange = () => {
				const isHidden = document.hidden
				if (isMountedRef.current) {
					setIsPaused(isHidden)
				}

				if (isHidden) {
					clearRefreshInterval()
				} else {
					if (refreshOnFocus && enabledRef.current) {
						refresh(true).then(null)
					}
					setupRefreshInterval()
				}
			}

			document.addEventListener('visibilitychange', handleVisibilityChange)

			return () => {
				document.removeEventListener('visibilitychange', handleVisibilityChange)
			}
		}, [])

		useEffect(() => {
			setupRefreshInterval()
			return clearRefreshInterval
		}, [interval])

		useEffect(() => {
			if (!refreshOnOnline) return

			const handleOnline = () => {
				if (!document.hidden && enabledRef.current) {
					refresh(true).then(null)
				}
			}

			window.addEventListener('online', handleOnline)

			return () => {
				window.removeEventListener('online', handleOnline)
			}
		}, [refreshOnOnline])

		useEffect(() => {
			return () => {
				isMountedRef.current = false
				clearRefreshInterval()
			}
		}, [])

		const toggle = state => {
			enabledRef.current = state ?? !enabledRef.current
			setupRefreshInterval()
		}

		return {
			...controller,
			refresh: {
				isRefreshing,
				lastRefresh,
				isPaused,
				execute: () => refresh(false),
				silent: () => refresh(true),
				toggle,
				getStatus: () => ({
					enabled: enabledRef.current,
					isRefreshing,
					lastRefresh,
					interval,
					isPaused,
				}),
				clear: clearRefreshInterval,
			},
		}
	}
}
