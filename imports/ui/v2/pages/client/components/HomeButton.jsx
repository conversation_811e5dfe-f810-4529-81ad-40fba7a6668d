import Box from '@mui/material/Box'
import { IconClientBrand } from '../../../../branded-components/Icons'
import { faHouse } from '@fortawesome/pro-regular-svg-icons'
import React from 'react'

export const HomeButton = ({ onClick }) => {
	return (
		<Box
			sx={{
				width: '30px',
				height: '30px',
				borderRadius: '5px',
				display: 'flex',
				alignItems: 'center',
				justifyContent: 'center',
				'&: hover': {
					cursor: 'pointer',
					backgroundColor: 'rgba(200,200,200,0.3)',
				},
			}}
			onClick={onClick}>
			<IconClientBrand
				size="lg"
				icon={faHouse}
				color="inherit"
			/>
		</Box>
	)
}
