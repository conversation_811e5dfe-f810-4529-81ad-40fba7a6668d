import Box from '@mui/material/Box'
import { faArrowLeft } from '@fortawesome/pro-regular-svg-icons'
import React from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { useNavigate } from 'react-router-dom'

export const BackButton = ({ disabled }) => {
	const navigate = useNavigate()
	const back = () => {
		navigate(-1)
	}

	return (
		<Box
			sx={{
				width: '30px',
				height: '30px',
				borderRadius: '5px',
				display: 'flex',
				alignItems: 'center',
				opacity: disabled ? '50%' : '100%',
				justifyContent: 'center',
				color: 'inherit',
				'&: hover': {
					cursor: disabled ? 'not-allowed' : 'pointer',
					backgroundColor: 'rgba(200,200,200,0.3)',
				},
			}}
			onClick={disabled ? null : back}>
			<FontAwesomeIcon
				icon={faArrowLeft}
				size="lg"
				color="inherit"
			/>
		</Box>
	)
}
