import { useQuery } from '@tanstack/react-query'
import { fetchPlanDetails, fetchPlanDetailsCount } from '@pages/coach/plans/PlanBuilder/services/api'

export default function usePlanDetails({ planId, noAdapter = false, querySettings }) {
	return useQuery({
		queryKey: ['planDetails', planId],
		queryFn: () => fetchPlanDetails(planId, noAdapter),
		...querySettings,
	})
}

export const usePlanDetailsCount = ({ settings }) => {
	return useQuery({
		queryKey: ['planDetailsCount'],
		queryFn: () => fetchPlanDetailsCount(settings),
	})
}
