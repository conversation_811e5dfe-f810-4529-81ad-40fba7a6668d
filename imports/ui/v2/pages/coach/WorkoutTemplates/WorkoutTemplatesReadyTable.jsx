import React from 'react'
import { ButtonGroupCell, DataTable, TextCell } from '@design/components/data'
import DateCell from '@design/components/data/DataTable/cells/base/DateCell'
import { selectCellPrefab } from '@design/components/data/DataTable/cells/utils/SelectCell'
import { sortCellPrefab } from '@design/components/data/DataTable/cells/utils/SortCell'
import { createGrapherController } from '../../../helpers'
import { getWorkouts } from '../../../../../api/workouts/workouts_Q'
import { Seconds } from '../../../../utility/Dates/timeUnits'
import { withDebug } from '../../../helpers/GrapherController/plugins/withDebug'
import { withFilters } from '../../../helpers/GrapherController/plugins/withFilters'
import { withPagination } from '../../../helpers/GrapherController/plugins/withPagination'
import { withRefresh } from '../../../helpers/GrapherController/plugins/withRefresh'
import { withSelection } from '../../../helpers/GrapherController/plugins/withSelection'
import { withTransformations } from '../../../helpers/GrapherController/plugins/withTransformations'
import { withSortable } from '../../../helpers/GrapherController/plugins/withSortable'

function WorkoutTemplatesReadyTable({ templateId }) {
	const readyTemplates = createGrapherController({
		queryFn: getWorkouts,
		singleton: false,
	})([
		withDebug({ enabled: true }),
		withFilters({
			initialFilters: {
				templateId,
				creationStatus: 'READY',
			},
		}),
		withPagination(),
		withRefresh({ interval: Seconds(60) }),
		withSelection(),
		withTransformations({
			computedFields: {
				detailsCount: ({ details }) => details.length,
			},
		}),
		withSortable(),
	])

	const columns = [
		sortCellPrefab,
		{
			key: 'title',
			component: TextCell,
			header: {
				title: 'Task',
				align: 'left',
			},
		},
		{
			key: 'createdAt',
			component: DateCell,
			width: '120px',
			header: {
				title: 'Created',
				align: 'center',
			},
		},
		{
			key: 'detailsCount',
			component: TextCell,
			width: '80px',
			align: 'center',
			header: {
				title: 'Tasks',
				align: 'center',
			},
		},
		selectCellPrefab,
		{
			key: 'actions',
			component: ButtonGroupCell,
			width: '140px',
			header: {
				title: 'Actions',
				align: 'center',
			},
		},
	]

	return (
		<DataTable
			title="Ready Templates"
			columns={columns}
			rows={readyTemplates.data}
			sortable={readyTemplates.sortable}
			loading={!readyTemplates.data}
			selection={readyTemplates.selection}
			pagination={readyTemplates.pagination}
		/>
	)
}

export default React.memo(WorkoutTemplatesReadyTable)
