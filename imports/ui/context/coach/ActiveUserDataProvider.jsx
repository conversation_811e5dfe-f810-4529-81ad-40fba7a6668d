import React, { createContext, useMemo, useState } from 'react'
import useStaticGrapher2 from '../../hooks/useStaticGrapher2'
import { getClientInfo, getCoachByAccessLink } from '../../../api/coachClients/coachClients_Q'
import DefaultBranding from '../../constants/DefaultBranding'
import { generatePalette } from '../../utility/color/ColorPalette'

export const ActiveUserContext = createContext({})

export const ActiveUserDataProvider = ({ children, userId }) => {
	const url = window.location.href
	const isPublicApp = url.includes('/pt/')
	const accessLink = isPublicApp ? url.split('/pt/')[1].split('/')[0].split('?')[0] : null
	const coach = !isPublicApp ? userId : null

	const userType = useMemo(() => {
		if (isPublicApp) return 'client'
		return 'coach'
	}, [accessLink, coach, userId])

	const userGraph = useStaticGrapher2({
		getQuery: getClientInfo,
		singleton: true,
		refresh: false,
		settings: {
			filters: { slug: accessLink ?? 'QuickCoach-Slug' },
		},
		debug: false,
		inputs: [accessLink],
	})

	const clientInfo = useMemo(() => {
		if (userGraph.ready) {
			return {
				_id: userGraph?.data?.clientId,
				profile: userGraph?.data?.client?.profile,
				slug: accessLink,
			}
		}
		return null
	}, [userGraph.ready, userGraph.data])

	const coachGraph = useStaticGrapher2({
		getQuery: getCoachByAccessLink,
		singleton: true,
		refresh: false,
		settings: {
			filters: coach ? { coachId: coach } : { slug: accessLink ?? 'QuickCoach-Slug' },
		},
		debug: false,
		inputs: [accessLink, coach],
	})

	const [branding, setBranding] = useState(DefaultBranding)

	const { subscription, coachInfo } = useMemo(() => {
		if (coachGraph.ready) {
			const newBranding = {
				...DefaultBranding,
				...coachGraph.data?.branding,
				palette: generatePalette(coachGraph.data?.branding?.themeColor),
			}
			setBranding(newBranding)
			return (
				coachGraph.data ?? {
					subscription: false,
					coachInfo: null,
					branding: null,
				}
			)
		}
		return { subscription: false, coachInfo: null, branding: null }
	}, [coachGraph.ready, coachGraph.data])

	return (
		<ActiveUserContext.Provider
			value={{
				coachInfo,
				clientInfo,
				setBranding,
				subscription,
				branding: subscription || branding?.isPreview ? branding : DefaultBranding,
				userType,
				updateCoach: coachGraph.update,
				updateClient: userGraph.update,
			}}>
			{children}
		</ActiveUserContext.Provider>
	)
}
