.custom-input-form {
	display: grid;
}

.custom-input-form label {
	color: var(--gray-700);
	font-size: 14px;
	font-weight: 500;
	line-height: 20px;
}

.custom-input-form label span {
	color: red;
}

.custom-input-form input {
	background: #ffffff;
	border: 1px solid var(--gray-300);
	box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
	border-radius: 8px;
	height: 44px;
	margin: 6px 0 0;
	padding: 0.5rem;
}

.custom-input-form input:focus {
	border: 1px solid var(--gray-300);
	outline: none;
}

.toggle-fields-btn {
	font-size: 10px;
	padding-left: 0.5rem;
	padding-right: 0.5rem;
	min-width: 100px !important;
}

.custom-boolean-input label {
	margin-left: 0.25rem;
}

.overflow-horizontal-auto {
	overflow-x: auto;
}
