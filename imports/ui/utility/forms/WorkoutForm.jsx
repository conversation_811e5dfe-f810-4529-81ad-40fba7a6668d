import React, { useState } from 'react'
import { Meteor } from 'meteor/meteor'
import { AutoForm, ErrorField, TextField } from 'uniforms-unstyled'
import Grid from '@mui/material/Grid'
import { SimpleSchema2Bridge } from 'uniforms-bridge-simple-schema-2'
import { WorkoutFormSchemaForTitle } from '../../../api/workouts/workouts'
import notifyError from '../toast/notifyError'
import { hideLoading, showLoading } from '../../../api/states/workoutPageState'

if (Meteor.isClient) import('./workout-form.css')

const schema = new SimpleSchema2Bridge(WorkoutFormSchemaForTitle)

const WorkoutForm = ({ workout, clientId, hideTitleLabel, onChange }) => {
	const [nameCustomer, setNameCustomer] = useState('')
	const [fireOnChange, setFireOnChange] = useState(false)

	if (clientId) {
		Meteor.callAsync('users.findById', { _id: clientId })
			.then(success => {
				setNameCustomer(success.profile.firstName + ' ' + success.profile.lastName)
			})
			.catch(error => {
				console.log(error)
			})
	}

	const submitFrom = (data, formRef) => {
		showLoading()
		const payload = {
			_id: data._id,
			path: 'WorkoutForm',
			modifier: {
				$set: {
					title: data.title,
				},
			},
		}
		Meteor.callAsync('workout.update', payload)
			.then(result => {
				hideLoading()
				setFireOnChange(true)
			})
			.catch(err => {
				notifyError(err.reason)
			})
	}

	const onBlur = () => {
		if (fireOnChange) {
			onChange()
		}
	}

	const labelInput = !hideTitleLabel ? (
		<label>
			Plan name<span>*</span>
		</label>
	) : (
		''
	)

	return (
		<section>
			<Grid
				item
				sx={{ display: 'flex' }}>
				{nameCustomer !== '' ? (
					<Grid
						item
						marginBottom={3}
						fontWeight={'bold'}>
						<Grid item>Plan for: {nameCustomer}</Grid>
					</Grid>
				) : null}
			</Grid>
			<AutoForm
				schema={schema}
				model={{
					title: workout?.title ? workout?.title : '',
					_id: workout._id,
				}}
				onChangeModel={data => submitFrom(data)}>
				<TextField
					name={'title'}
					label={labelInput}
					className="custom-input-form"
					onBlur={() => onBlur()}
				/>
				<ErrorField
					className="warn"
					name={'title'}
				/>
			</AutoForm>
		</section>
	)
}

export default WorkoutForm
