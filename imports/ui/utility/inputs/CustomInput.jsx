import OutlinedInput from '@mui/material/OutlinedInput'
import Select from '@mui/material/Select'
import MuiPhoneNumber from 'dyxbenjamin-mui-phone-number'
import { styled } from '@mui/material/styles'

export const OutlineBlankInput = styled('input')({
	background: 'transparent',
	border: '1px solid var(--gray-300)',
	boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
	borderRadius: '8px',
	color: 'var(--gray-500)',
	fontSize: '16px',
	fontWeight: 400,
	lineHeight: '24px',
	height: '44px',
	minHeight: '20px',
	marginRight: '12px',
	padding: '12px 12px',
	transition: 'all 200ms ease',
	'&:hover': {
		outline: 'none',
	},
	'&:focus': {
		outline: 'none',
	},
	'&.invalid': {
		border: '1px solid var(--red-600)',
	},
})

export const ColorInput = styled('input')({
	background: 'transparent',
	border: '1px solid var(--gray-300)',
	boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
	borderRadius: '8px',
	height: '44px',
	minHeight: '20px',
	padding: '4px',
	transition: 'all 200ms ease',
	'&:hover': {
		outline: 'none',
	},
	'&:focus': {
		outline: 'none',
	},
	'&.invalid': {
		border: '1px solid var(--red-600)',
	},
	'&::-webkit-color-swatch-wrapper': {
		border: 'none',
		borderRadius: '8px',
	},
	'&::-moz-color-swatch': {
		border: '1px solid var(--gray-300)',
		cursor: 'pointer',
		borderRadius: '8px',
	},
})

export const OutlineBlankTextArea = styled('textarea')({
	background: 'white',
	border: '1px solid var(--gray-400)',
	boxShadow: 'none',
	borderRadius: '8px',
	color: 'var(--gray-600)',
	fontSize: '16px',
	fontWeight: 400,
	lineHeight: '24px',
	padding: '12px 12px',
	resize: 'none',
	transition: 'all 200ms ease',
	'&:hover': {
		outline: 'none',
	},
	'&:focus': {
		outline: 'none',
	},
})

export const OutlinePrimaryInput = styled('input')({
	background: 'white',
	border: '1px solid var(--primary-100)',
	boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
	borderRadius: '8px',
	color: 'var(--primary-600)',
	fontSize: '30px',
	fontWeight: 500,
	lineHeight: '38px',
	padding: '13px 8px',
	transition: 'all 200ms ease',
	textAlign: 'center',
	'&:hover': {
		outline: 'none',
	},
	'&:focus': {
		outline: 'none',
	},
	'&::placeholder': {
		color: 'var(--gray-300)',
	},
})

export const OutlineWhiteIconInput = styled(OutlinedInput)({
	background: 'white',
	boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
	borderRadius: '8px',
	color: 'var(--gray-500)',
	fontSize: '16px',
	fontWeight: 'normal',
	lineHeight: '24px',
	padding: '10px 14px',
	transition: 'all 200ms ease',
	'&:hover': {
		border: 'none !important',
	},
	'&.Mui-focused': {
		border: 'none !important',
	},
	'&:focus': {
		outline: 'none',
	},
	'&:active': {
		outline: 'none',
	},
	'& input::placeholder': {
		color: 'var(--gray-500)',
	},
	'& input': {
		padding: 0,
	},
	'& fieldset': {
		border: '1px solid var(--gray-300) !important',
	},
})

export const OutlineWhiteSelect = styled(Select)({
	background: 'white',
	boxShadow: 'none',
	borderRadius: '8px',

	fontSize: '16px',
	fontWeight: 'normal',
	lineHeight: '24px',
	transition: 'all 200ms ease',

	'&:hover': {
		border: 'none !important',
	},
	'& .MuiSelect-select': {
		background: 'white',
		color: 'var(--gray-500)',
		padding: '10px 14px',
	},
	'&:focus': {
		outline: 'none',
	},
	'&:active': {
		outline: 'none',
	},
	'& input::placeholder': {
		color: 'var(--gray-500)',
	},
	'& input': {
		padding: 0,
	},
	'& fieldset': {
		border: '1px solid var(--gray-300) !important',
	},
})

export const OutlineWhitePhoneNumber = styled(MuiPhoneNumber)({
	background: 'white',
	border: '1px solid var(--gray-300) !important',
	boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
	borderRadius: '8px',
	height: '44px',
	padding: '10px 14px !important',
	transition: 'all 200ms ease',
	'& .MuiInput-root:before': {
		borderBottom: 'none !important',
	},
	'& .MuiInput-root:after': {
		borderBottom: 'none !important',
	},
	'& input': {
		fontSize: '16px',
		fontWeight: 'normal',
		lineHeight: '24px',
		padding: 0,
	},
})
