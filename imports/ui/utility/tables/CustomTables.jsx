import { styled } from '@mui/material/styles'
import Table from '@mui/material/Table'

export const WorkoutsCustomTable = styled(Table)(({ theme }) => ({
	'& .MuiTableCell-root': {
		color: 'var(--gray-700)',
		fontSize: '14px',
		lineHeight: '20px',
		padding: '16px',
	},
	'& .MuiTableBody-root': {
		'& .MuiTableCell-root': {
			borderLeft: '0.5px solid var(--gray-300)',
			borderRight: '0.5px solid var(--gray-300)',
			boxShadow: 'inset 1px 0px 0px #E4E7EC',
			'& input': {
				border: 'none',
				color: 'var(--gray-700)',
				fontWeight: 400,
				fontSize: '14px',
				lineHeight: '20px',
				textAlign: 'left',
			},
		},
		'& .MuiTableCell-root:not(:first-of-type)': {
			minWidth: '140px',
		},
		'& .MuiTableCell-root:first-of-type': {
			border: '1px solid var(--gray-300)',
			borderLeft: 'none',
			width: '32px',
			padding: '16px 0',
		},
		'& .MuiTableCell-root:last-child': {
			borderRight: 'none',
			minWidth: '100px',
			padding: '0.5rem',
			width: '100px',
		},
		'& .superset-container': {
			backgroundColor: 'var(--primary-50)',
			'& .MuiTableCell-root:first-of-type': {
				border: '1px solid var(--gray-300)',
				width: '24px',
			},
			'& .MuiTableCell-root:last-child': {
				borderRight: '1px solid var(--gray-300)',
				width: '92px',
			},
		},
		'& .movement-superset-field': {
			minWidth: '350px',
			width: '350px',
		},
	},
	'& .MuiTableHead-root': {
		'& .MuiTableCell-root': {
			fontWeight: 500,
			textAlign: 'left',
		},
	},
	'& .MuiTableFooter-root': {
		// backgroundColor: 'var(--gray-50)',
		// borderRadius: '8px',
		boxShadow: '0px -1px 0px #E0E4EB',
		'& .MuiTableCell-root': {
			borderRadius: '8px',
			backgroundColor: 'var(--gray-50)',
			padding: '12px',
			textAlign: 'center',
			'& Button': {
				color: 'var(--gray-700)',
				fontWeight: 500,
				fontSize: '14px',
				lineHeight: '20px',
				padding: 0,
				textTransform: 'none',
				'& i': {
					marginLeft: '0.5rem',
					marginRight: '0.5rem',
				},
			},
			'& Button:hover': {
				background: 'transparent',
			},
		},
	},
}))

export const WorkoutsSupersetCustomTable = styled(Table)(() => ({
	'& .MuiTableCell-root': {
		color: 'var(--gray-700)',
		fontSize: '14px',
		lineHeight: '20px',
		padding: '16px',
	},
	'& .MuiTableBody-root': {
		'& .MuiTableCell-root': {
			borderLeft: '0.5px solid var(--gray-300)',
			borderRight: '0.5px solid var(--gray-300)',
			boxShadow: 'inset 1px 0px 0px #E4E7EC',
			'& input': {
				border: 'none',
				color: 'var(--gray-700)',
				fontWeight: 400,
				fontSize: '14px',
				lineHeight: '20px',
				textAlign: 'left',
			},
		},
		'& .MuiTableCell-root:not(:first-of-type)': {
			minWidth: '140px',
		},
		'& .MuiTableCell-root:first-of-type': {
			border: '1px solid var(--gray-300)',
			borderLeft: 'none',
			width: '32px',
			padding: '16px 0',
		},
		'& .MuiTableCell-root:nth-of-type(2)': {
			width: '300px',
		},
		'& .MuiTableCell-root:last-child': {
			borderRight: 'none',
			minWidth: '100px',
			padding: '0.5rem',
			width: '100px',
		},
		'& .superset-container': {
			backgroundColor: 'var(--primary-50)',
			'& .MuiTableCell-root:first-of-type': {
				border: '1px solid var(--gray-300)',
				width: '24px',
			},
			'& .MuiTableCell-root:last-child': {
				borderRight: '1px solid var(--gray-300)',
				width: '92px',
			},
		},
		'& .movement-superset-field': {
			minWidth: '350px',
			width: '350px',
		},
	},
	'& .MuiTableHead-root': {
		'& .MuiTableCell-root': {
			fontWeight: 500,
			textAlign: 'left',
		},
	},
	'& .MuiTableFooter-root': {
		// backgroundColor: 'var(--gray-50)',
		// borderRadius: '8px',
		boxShadow: '0px -1px 0px #E0E4EB',
		'& .MuiTableCell-root': {
			borderRadius: '8px',
			backgroundColor: 'var(--gray-50)',
			padding: '12px',
			textAlign: 'center',
			'& Button': {
				color: 'var(--gray-700)',
				fontWeight: 500,
				fontSize: '14px',
				lineHeight: '20px',
				padding: 0,
				textTransform: 'none',
				'& i': {
					marginLeft: '0.5rem',
					marginRight: '0.5rem',
				},
			},
			'& Button:hover': {
				background: 'transparent',
			},
		},
	},
}))

export const CustomTable = styled(Table)(({ theme }) => ({
	minWidth: 650,
	border: 'none',
	'& .MuiTableCell-root': {
		color: 'var(--gray-700)',
		fontSize: '14px',
		lineHeight: '20px',
		padding: '16px 12px',
	},
	'& .MuiTableBody-root': {
		border: 'none',
		'& .MuiTableRow-root': {
			'&:last-of-type': {
				border: 'none',
			},
		},
		'& .MuiTableCell-root': {
			borderLeft: '1px solid var(--gray-300)',
			// boxShadow: 'inset 1px 0px 0px #E4E7EC',
			'&:first-of-type': {
				borderLeft: 'none',
			},
		},
		'& .MuiTableCell-root:first-of-type': {
			borderLeft: 'none',
		},
		'& .MuiTableCell-root:last-child': {
			borderRight: 'none',
		},
		'& .MuiTableRow-root:last-child': {
			'& .MuiTableCell-root': {
				borderBottom: 'none',
			},
		},
	},
	'& .MuiTableHead-root': {
		'& .MuiTableCell-root': {
			fontWeight: 500,
			textAlign: 'left',
		},
	},
	'& .MuiTableFooter-root': {
		boxShadow: '0px -1px 0px #E0E4EB',
		'& .MuiTableCell-root': {
			borderRadius: '8px',
			backgroundColor: 'var(--gray-50)',
			padding: '12px',
			textAlign: 'center',
			'& Button': {
				color: 'var(--gray-700)',
				fontWeight: 500,
				fontSize: '14px',
				lineHeight: '20px',
				padding: 0,
				textTransform: 'none',
				'& i': {
					marginLeft: '0.5rem',
					marginRight: '0.5rem',
				},
			},
			'& Button:hover': {
				background: 'transparent',
			},
		},
	},
}))

export const CustomNotesTable = styled(Table)(({ theme }) => ({
	'& .MuiTableCell-root': {
		borderBottom: '0.5px solid var(--gray-300)',
		color: 'var(--gray-700)',
		fontSize: '14px',
		lineHeight: '20px',
		padding: '16px 12px',
	},
	'& .MuiTableBody-root': {
		'& .MuiTableCell-root': {
			borderLeft: '0.5px solid var(--gray-300)',
			borderRight: '0.5px solid var(--gray-300)',
			boxShadow: 'inset 1px 0px 0px #E4E7EC',
		},
		'& .MuiTableCell-root:first-of-type': {
			borderLeft: 'none',
		},
		'& .MuiTableCell-root:last-child': {
			borderRight: 'none',
		},
		'& .MuiTableRow-root:last-child': {
			'& .MuiTableCell-root': {
				borderBottom: 'none',
			},
		},
	},
	'& .MuiTableHead-root': {
		'& .MuiTableCell-root': {
			fontWeight: 500,
			textAlign: 'left',
		},
	},
	'& .MuiTableFooter-root': {
		boxShadow: '0px -1px 0px #E0E4EB',
		'& .MuiTableCell-root': {
			borderRadius: '8px',
			backgroundColor: 'var(--gray-50)',
			padding: '12px',
			textAlign: 'center',
			'& Button': {
				color: 'var(--gray-700)',
				fontWeight: 500,
				fontSize: '14px',
				lineHeight: '20px',
				padding: 0,
				textTransform: 'none',
				'& i': {
					marginLeft: '0.5rem',
					marginRight: '0.5rem',
				},
			},
			'& Button:hover': {
				background: 'transparent',
			},
		},
	},
}))
