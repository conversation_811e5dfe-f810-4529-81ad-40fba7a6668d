import React, { useContext } from 'react'
import Checkbox from '@mui/material/Checkbox'
import { ActiveUserContext } from '../context/coach/ActiveUserDataProvider'

export const CheckBoxClientBrand = props => {
	const { branding } = useContext(ActiveUserContext)
	return (
		<Checkbox
			{...props}
			sx={{
				'& .MuiSvgIcon-root': {
					fill: branding?.palette?.[800] ?? 'var(--primary-600)',
				},
				'&.Mui-checked .MuiSvgIcon-root': {
					fill: branding?.palette?.[800] ?? 'var(--primary-600)',
				},
				'&.MuiCheckbox-colorPrimary.Mui-checked': {
					color: branding?.palette?.[800] ?? 'var(--primary-600)',
				},
			}}
		/>
	)
}
