import React, { useContext } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { ActiveUserContext } from '../context/coach/ActiveUserDataProvider'

export const IconClientBrand = ({ icon, disabled, ...rest }) => {
	const { branding } = useContext(ActiveUserContext)
	const css = {
		cursor: rest?.onClick ? 'pointer' : 'default',
		opacity: disabled ? '0.3' : '1',
	}

	return (
		<FontAwesomeIcon
			icon={icon}
			style={css}
			color={branding?.palette?.[800] ?? 'var(--primary-600)'}
			{...rest}
		/>
	)
}
