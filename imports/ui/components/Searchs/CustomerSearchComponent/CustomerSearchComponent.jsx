import React from 'react'
import { useState } from 'react'
import Autocomplete from '@mui/material/Autocomplete'
import TextField from '@mui/material/TextField'
import Stack from '@mui/material/Stack'
import { Dialog, DialogContent, DialogTitle } from '@mui/material'
import CircularProgress from '@mui/material/CircularProgress'
import useStaticGrapher2 from '../../../hooks/useStaticGrapher2'
import { getWorkoutsForAutocomplete } from '../../../../api/workouts/workouts_Q'
import { getTemplatesForAutocomplete } from '../../../../api/template/template_Q'
import { getCustomersForAutoComplete } from '../../../../api/users/users_Q'
import { ContainedPrimaryBtn } from '../../../utility/buttons/BackButton'

if (Meteor.isClient) import('./customer-search-component.css')

const CustomerSearchComponent = ({
	open,
	assignEvent,
	onClose,
	action,
	itemPaternName,
	collection,
	loading,
	changeDrafts,
}) => {
	const [selectedList, setSelectedList] = useState([])
	let items = []
	const closeModal = () => {
		items = []
		onClose()
	}

	const assign = () => {
		assignEvent(selectedList)
	}

	const getLabels = () => {
		switch (action) {
			case 'ASSIGN_TEMPLATE_TO_CLIENTS':
				return {
					title: `Assign ${itemPaternName} to clients`,
					label: 'Selected client will be added to this template.',
					labelButton: 'Assign to client(s)',
					loadingButton: 'Assigning to client(s)',
				}
			case 'ASSIGN_WORKOUTS_TO_TEMPLATE':
				return {
					title: `Add plans to Template`,
					label: 'Selected plans will be added to the template below.',
					labelButton: 'Add to template',
				}
			case 'ASSIGN_WORKOUT_TO_TEMPLATE':
				return {
					title: `Add plan to Template`,
					label: 'Selected plan will be added to the template below.',
					labelButton: 'Add to template',
				}
			case 'ASSIGN_WORKOUT_TO_CLIENTS':
				return {
					title: `Add ${itemPaternName} to clients`,
					label: 'This plan will be assigned to selected client(s).',
					labelButton: 'Add to client(s)',
				}
			case 'ASSIGN_WORKOUT_TO_CLIENT':
				return {
					title: `Add ${itemPaternName} to client`,
					label: 'This plan will be assigned to selected client(s).',
					labelButton: 'Add to client(s)',
				}
			default:
				return { title: '', label: '', labelButton: '' }
		}
	}

	let coachId = 'Quick-coach-default'
	coachId = Meteor.userId()

	const getClients = () => {
		const coachInfo = useStaticGrapher2({
			getQuery: getCustomersForAutoComplete,
			refresh: false,
			singleton: true,
			settings: {
				filters: { _id: coachId },
				options: {
					limit: 1000,
					sort: { createdAt: 1 },
				},
			},
			debug: false,
			inputs: [],
		})
		return coachInfo?.data?.clients
			?.map((item, index) => {
				return {
					name: `${index + 1} - ${item?.profile?.firstName} ${item?.profile?.lastName}`,
					id: item?._id,
					active: item?.profile?.active,
				}
			})
			.filter(item => item?.active === true)
	}

	const getWorkoutsList = () => {
		const items = useStaticGrapher2({
			getQuery: getWorkoutsForAutocomplete,
			singleton: false,
			refresh: false,
			settings: {
				filters: {
					userId: coachId,
					creationStatus: 'DRAFT',
					templateId: { $eq: null },
					clientId: { $eq: null },
				},
				options: {
					limit: 1000,
					sort: { createdAt: 1 },
				},
			},
			debug: false,
			inputs: [changeDrafts],
		})
		return items?.data?.map((item, index) => {
			return {
				name: `${index + 1} - ${item?.title ? item?.title : 'Untitled workout'}`,
				creationStatus: item?.creationStatus,
				id: item?._id,
			}
		})
	}

	const getTemplateList = () => {
		const items = useStaticGrapher2({
			getQuery: getTemplatesForAutocomplete,
			singleton: false,
			refresh: false,
			settings: {
				filters: { coachId: coachId },
				options: {
					limit: 1000,
					sort: { createdAt: 1 },
				},
			},
			debug: false,
			inputs: [],
		})
		return items?.data?.map((item, index) => {
			return {
				name: `${index + 1} - ${item?.name ? item?.name : 'Untitled template'}`,
				id: item?._id,
			}
		})
	}
	switch (collection) {
		case 'clients':
			items = getClients()
			break
		case 'templates':
			items = getTemplateList()
			break
		case 'workouts':
			items = getWorkoutsList()
			break
	}

	return (
		<Dialog
			open={open}
			style={{ padding: 5 }}
			className="modal-search-component"
			onClose={() => closeModal()}>
			<DialogTitle>{getLabels().title}</DialogTitle>
			<DialogContent sx={{ overflowY: 'visible' }}>
				<p className="label-search">{getLabels().label}</p>
				<Stack
					spacing={3}
					sx={{ marginTop: 2, marginBottom: 2 }}>
					<Autocomplete
						disableCloseOnSelect
						className="search-autocomplete"
						multiple={action !== 'ASSIGN_WORKOUT_TO_TEMPLATE'}
						onChange={(event, value) => setSelectedList(value)}
						id="tags-outlined"
						options={items ? items : []}
						getOptionLabel={option => option.name}
						filterSelectedOptions
						isOptionEqualToValue={(option, value) => option.name === value.name}
						renderInput={params => (
							<TextField
								{...params}
								label=""
								placeholder="Search..."
								className="search-text-field"
								InputProps={{
									...params.InputProps,
									endAdornment: (
										<React.Fragment>
											{loading ? (
												<CircularProgress
													color="inherit"
													size={20}
												/>
											) : null}
											{params.InputProps.endAdornment}
										</React.Fragment>
									),
								}}
							/>
						)}
					/>
				</Stack>
				{loading ? (
					<ContainedPrimaryBtn
						disabled={true}
						className="action-btn disable-btn">
						{getLabels().loadingButton}
						<CircularProgress
							sx={{ marginLeft: '5px' }}
							color="inherit"
							size={20}
						/>
					</ContainedPrimaryBtn>
				) : (
					<ContainedPrimaryBtn
						disabled={selectedList?.length === 0}
						onClick={() => assign()}
						className="action-btn">
						{getLabels().labelButton}
					</ContainedPrimaryBtn>
				)}
			</DialogContent>
		</Dialog>
	)
}

export default CustomerSearchComponent
