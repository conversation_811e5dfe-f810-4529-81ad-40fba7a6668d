import React, { useEffect, useState } from 'react'
import Modal from '@mui/material/Modal'
import Box from '@mui/material/Box'
import notifyError from '../../utility/toast/notifyError'
import { setChangeExerciseValue } from '../../../api/states/workoutPageState'
import FlexRow from '../layouts/FlexRow'
import FlexColumn from '../layouts/FlexColumn'
import RichTextEditor from '../forms/RichTextEditor'
import { Tab, Tabs } from '@mui/material'
import ImagesManagerWidget from '../widgets/ImagesManagerWidget'
import { useSubscription } from '../../context/subscription/SubscriptionProvider'
import ModalContentLayout from '../layouts/ModalContentLayout'
import BrandedButton from '../atoms/BrandedButton'
import ProBadge from '../atoms/ProBadge'
import VideoPlayer from '../../../ui-modern/features/video-player/VideoPlayer'

const css = {
	tabs: {
		background: 'var(--gray-100)',
		borderRadius: '18px',
		width: '100%',
		padding: '4px',
		boxSizing: 'border-box',
		minHeight: '4px',
		gap: '4px',
		'& .Mui-selected': {
			background: 'white',
			borderRadius: '12px',
			borderBottom: 'none',
		},
		'& .MuiTabs-indicator': {
			height: 0,
		},
		'& .MuiTab-root': {
			borderRadius: '14px',
			color: 'var(--gray-900)',
			fontWeight: '500',
			minHeight: '32px',
			padding: '8px',
			width: '50%',
			'&:hover': {
				background: 'var(--gray-200)',
			},
		},
	},
}

const defaultSettings = {
	mediaType: 'video',
	enableVideo: true,
	enableImages: true,
	enableNotes: true,
}

/**
 * @component
 * @description A modal to show the properties of an exercise.
 * @category Modals
 * @version 2024.I-01
 * @param {boolean} open - The state of the modal.
 * @param {object} exercise - The exercise to be shown.
 * @param {function} updateExercise - The function to update the exercise.
 * @param {function} onClose - The function to clear the modal.
 * @param {object} [settings] - The settings for the modal.
 * @param {string} [settings.mediaType='video'] - The default media type to be shown.
 * @param {boolean} [settings.enableVideo=true] - If true, the URL video property is shown.
 * @param {boolean} [settings.enableImages=true] - If true, the images property is shown.
 * @param {boolean} [settings.enableNotes=true] - If true, the notes property is shown.
 * @example <ExercisePropertiesModal open={true} exercise={exercise} updateExercise={updateExercise} onClose={handleClose} settings={{mediaType: 'video', enableVideo: true, enableImages: true, enableNotes: true}} />
 * @returns {ReactElement}
 */
const ExercisePropertiesModal = ({ open, exercise, updateExercise, onClose, settings = defaultSettings }) => {
	const [images, setImages] = useState(exercise?.images || [])

	useEffect(() => {
		setImages(exercise?.images || [])
	}, [exercise?.images])

	const handleAddImage = async imageInfo => {
		const payload = {
			_id: exercise?._id,
			details: {
				url: imageInfo.secure_url,
				publicId: imageInfo.public_id,
			},
		}
		const result = await Meteor.callAsync('exercise.addImage', payload)
		if (result?.error) {
			notifyError('Error on add image to exercise')
			return
		}

		setImages(currentImages => [...currentImages, payload.details])
		updateExercise?.()
	}

	const handleRemoveImage = index => {
		const payload = {
			_id: exercise?._id,
			index,
		}
		Meteor.callAsync('exercise.removeImage', payload)
			.then(result => {
				setImages(prevImages => prevImages.filter((img, i) => i !== index))
			})
			.catch(error => {
				console.log(error)
				notifyError('Error on update image from exercise')
			})
	}

	const handleReplaceImage = (imageInfo, index) => {
		const payload = {
			_id: exercise?._id,
			index,
			newDetails: {
				url: imageInfo.secure_url,
				publicId: imageInfo.public_id,
			},
		}

		Meteor.callAsync('exercise.replaceImage', payload)
			.then(res => {
				updateExercise?.()
				setImages([...res])
			})
			.catch(error => {
				console.log(error)
				notifyError('Error on update image from exercise')
			})
	}
	const onNotesChange = value => {
		const payload = {
			_id: exercise?._id,
			notes: value,
		}

		Meteor.callAsync('exercise.updateNotes', payload)
			.then(result => {
				updateExercise?.()
			})
			.catch(error => {
				console.log(error)
				notifyError('Error on update notes from exercise')
			})
	}

	const onDeleteVideo = () => {
		const payload = {
			_id: exercise._id,
			modifier: {
				$set: { urlVideo: '' },
			},
		}
		Meteor.callAsync('exercise.update', payload).catch(error => {
			console.log(error)
			notifyError('Error on delete video from exercise')
		})
	}
	const onUpdateVideo = urlVideo => {
		const payload = {
			_id: exercise?._id,
			urlVideo,
		}
		Meteor.callAsync('exercise.updateVideo', payload)
			.then(() => {
				setChangeExerciseValue()
			})
			.catch(error => {
				console.log(error)
				notifyError('Error on update video from exercise')
			})
	}

	const { verifyAndShowSubsModal } = useSubscription()

	const [activeTab, setActiveTab] = useState(settings.mediaType ?? 'video')
	const handleChangeTab = (_, newValue) => {
		if (newValue === 'images' && !verifyAndShowSubsModal()) return
		setActiveTab(newValue)
	}

	return (
		<Modal
			open={open}
			onClose={onClose}>
			<ModalContentLayout
				maxWidth={{ xs: '95%', sm: '520px' }}
				maxHeight={'90%'}
				sx={{ p: 0 }}
				scrollable>
				<ModalContentLayout.Header
					title={'Task notes'}
					subtitle={exercise?.text}
					handleClose={onClose}
				/>
				<ModalContentLayout.Body justify={'flex-start'}>
					{settings?.enableVideo && settings?.enableImages && (
						<FlexRow
							center
							fullWidth
							className={'mb-4'}>
							<Tabs
								value={activeTab}
								onChange={handleChangeTab}
								sx={css.tabs}>
								<Tab
									label="Video"
									value="video"
								/>
								<Tab
									icon={
										<Box pr={0.5}>
											<ProBadge size="sm" />
										</Box>
									}
									iconPosition="start"
									label="Images"
									value="images"
								/>
							</Tabs>
						</FlexRow>
					)}
					{activeTab === 'video' && settings?.enableVideo && (
						<VideoPlayer
							src={exercise?.urlVideo}
							onUpdateVideo={onUpdateVideo}
							onDeleteVideo={onDeleteVideo}
						/>
					)}
					{activeTab === 'images' && settings.enableImages && (
						<FlexColumn
							component={'section'}
							fullWidth
							gap={1}
							sx={{ mb: 4 }}>
							<ImagesManagerWidget
								images={images}
								enableUpload
								uploadSettings={{
									maxImages: 3,
									path: `${Meteor.userId()}/exercises/${exercise?._id}`,
									publicId: `${exercise?._id}-img-${images?.length}`,
									preset: 'images',
									onUpload: handleAddImage,
									onReplace: handleReplaceImage,
									onDelete: handleRemoveImage,
								}}
							/>
						</FlexColumn>
					)}
					{settings?.enableNotes && (
						<FlexColumn
							sx={{ mt: 2 }}
							component={'section'}
							fullWidth
							gap={1}>
							<p>Task Notes</p>
							<Box sx={{ maxWidth: '100%' }}>
								<RichTextEditor
									value={exercise?.notes}
									onTextChange={onNotesChange}
									maxHeight={'240px'}
								/>
							</Box>
						</FlexColumn>
					)}
				</ModalContentLayout.Body>
				<ModalContentLayout.Footer>
					<BrandedButton
						fullWidth
						onClick={onClose}>
						Complete
					</BrandedButton>
					{exercise?.new && (
						<BrandedButton
							fullWidth
							outlined
							color={'grey'}
							onClick={onClose}>
							Cancel
						</BrandedButton>
					)}
				</ModalContentLayout.Footer>
			</ModalContentLayout>
		</Modal>
	)
}

export default ExercisePropertiesModal
