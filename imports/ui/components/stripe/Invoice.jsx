import React, { useEffect, useState } from 'react'
import { Meteor } from 'meteor/meteor'

import Card from '@mui/material/Card'
import CardContent from '@mui/material/CardContent'
import Grid from '@mui/material/Grid'
import Divider from '@mui/material/Divider'
import Typography from '@mui/material/Typography'
import numeral from 'numeral'
import _ from 'lodash'
import moment from 'moment'

export default Invoice = ({ userId, subscriptionId }) => {
	const [invoice, setInvoice] = useState(false)
	// fetching invoices
	useEffect(() => {
		Meteor.callAsync('getUpcomingInvoiceForSubs', userId, subscriptionId)
			.then(invoice => {
				if (invoice?.lineItems?.length > 0) {
					setInvoice(invoice)
				} else {
					setInvoice(false)
				}
			})
			.catch(error => {
				console.log(error)
			})
		return () => {
			// Async / Meteor.call in useEffects Cleanup of useState variables
			setInvoice(false)
		}
	}, [])
	const data = invoice ? invoice : []

	return (
		<Grid
			container
			spacing={2}
			direction="row"
			justifyContent="space-evenly"
			alignItems="center"
			sx={{ padding: '15px' }}>
			<Grid
				item
				xs={12}
				sm={12}
				md={12}
				lg={12}
				xl={12}
				align="center"
				sx={{ marginBottom: '15px' }}>
				<Typography variant="h4">Upcoming Invoice</Typography>
			</Grid>
			<Card
				sx={{
					width: {
						xs: '95%',
						sm: '95%',
						md: '85%',
						lg: '65%',
						xl: '65%',
					},
					height: '100%',
				}}>
				{_.map(data.lineItems, lineItem => (
					<Grid
						item
						key={lineItem.id}>
						<CardContent>
							<Typography
								style={{ float: 'left' }}
								sx={{
									fontSize: '80%',
									maxWidth: '80%',
									wordWrap: 'break-word',
								}}>
								{lineItem.description} -{' '}
							</Typography>
							<Typography
								style={{ float: 'right' }}
								sx={{ fontSize: '80%' }}>
								{numeral(lineItem?.amount / 100).format('$0,0.00')}
							</Typography>
						</CardContent>
					</Grid>
				))}
				<Divider></Divider>
				<Grid item>
					<CardContent style={{ marginBottom: '10px' }}>
						{/* multiplied date by 1000 since its epoch */}
						<Typography
							style={{ float: 'left' }}
							sx={{ fontSize: '80%' }}>
							Total due on {moment(data.paymentDueOn * 1000).format('LLL')} -{' '}
						</Typography>
						<Typography
							style={{ float: 'right' }}
							sx={{ fontSize: '80%' }}>
							{' '}
							{numeral(data.totalAmountDue / 100).format('$0,0.00')}{' '}
						</Typography>
					</CardContent>
				</Grid>
			</Card>
		</Grid>
	)
}
