import React from 'react'
import Box from '@mui/material/Box'

const css = {
	container: {
		width: '100%',
		position: 'relative',
		'&:before': {
			content: '""',
			display: 'block',
			paddingTop: '100%',
		},
	},
	content: {
		position: 'absolute',
		top: 0,
		left: 0,
		bottom: 0,
		right: 0,
	},
}

/**
 * @component
 * @category Utils
 * @description A container with a square aspect ratio.
 * @version 2024.I-01
 * @param {ReactNode} children - The content to be displayed within the square container.
 * @param {object} props - Any other props to be passed to the Box component.
 * @example <SquareRatio><img src="https://via.placeholder.com/150" alt="Placeholder image" /></SquareRatio>
 * @returns {Element}
 */
export default function SquareRatio({ children, ...props }) {
	return (
		<Box
			sx={css.container}
			{...props}>
			<Box sx={css.content}>{children}</Box>
		</Box>
	)
}
