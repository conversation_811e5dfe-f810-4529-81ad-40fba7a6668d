import React from 'react'
import Modal from '@mui/material/Modal'
import ModalContentLayout from '../layouts/ModalContentLayout'
import BrandedButton from '../atoms/BrandedButton'
import FlexRow from '../layouts/FlexRow'

export default function ConfirmActionModal({ open, title, subtitle, content, onConfirm, onCancel }) {
	return (
		<Modal
			open={open}
			onClose={onCancel}>
			<ModalContentLayout
				maxWidth={{ xs: '95%', sm: '520px' }}
				maxHeight={'90%'}
				sx={{ p: 0 }}
				scrollable>
				<ModalContentLayout.Header
					title={title}
					subtitle={subtitle}
					handleClose={onCancel}
				/>
				<ModalContentLayout.Body>{content}</ModalContentLayout.Body>
				<ModalContentLayout.Footer>
					<FlexRow
						spacing={1}
						transpose>
						<BrandedButton
							fullWidth
							onClick={onConfirm}>
							Confirm
						</BrandedButton>
						<BrandedButton
							fullWidth
							outlined
							color={'brandGray'}
							onClick={onCancel}>
							Cancel
						</BrandedButton>
					</FlexRow>
				</ModalContentLayout.Footer>
			</ModalContentLayout>
		</Modal>
	)
}
