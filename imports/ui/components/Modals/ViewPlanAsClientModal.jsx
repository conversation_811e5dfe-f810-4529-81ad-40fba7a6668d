import React, { useContext } from 'react'
import Modal from '@mui/material/Modal'
import Box from '@mui/material/Box'
import DeviceFrame from '../media/DeviceFrame'
import { getClientPlanDetails } from '../../../api/workouts/workouts_Q'
import WorkoutDetails from '../../pages/customer/workoutDetails/WorkoutDetails'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faXmark } from '@fortawesome/pro-regular-svg-icons'
import { ActiveUserContext } from '../../context/coach/ActiveUserDataProvider'
import FlexColumn from '../layouts/FlexColumn'
import WithMobileScroll from '../layouts/WithMobileScrool'
import usePersistentState from '../../hooks/usePersistentState'
import { useGrapherQuery } from '@ui-libs/create-query/useGrapherQuery'

const deviceOptions = [
	{
		os: 'iOS',
		label: 'iPhone',
		value: 'iphone-14-pro-max',
		initialScale: 0.5,
	},
	{
		os: 'Android',
		label: 'Android',
		value: 'poco-m3-pro',
		initialScale: 0.5,
	},
]

const css = {
	modalContainer: {
		top: '50%',
		left: '50%',
		transform: 'translate(-50%, -50%)',
		position: 'absolute',
		border: 'none',
		borderRadius: '62px',
		padding: '16px 16px 16px 16px',
		display: 'flex',
		flexDirection: 'column',
		alignItems: 'center',
		justifyContent: 'center',
		'& h6': {
			color: 'var(--gray-700)',
			fontSize: '16px',
			lineHeight: '24px',
			marginBottom: '25px',
			textTransform: 'none',
		},
		'&:active': {
			border: 'none',
		},
	},
}

// ToDo: Rework this component
export default function ViewPlanAsClientModal({ open, onClose, workoutId, clientId }) {
	const filters = {
		_id: workoutId,
		clientId: clientId ? clientId : { $exists: false },
	}
	console.log('filters', filters)

	const { branding } = useContext(ActiveUserContext) || {}
	const [previewDevice] = usePersistentState('PreviewDevice')

	const device = deviceOptions.find(device => device.label === previewDevice) ?? deviceOptions[0]

	const { data, ready, update } = useGrapherQuery({
		query: getClientPlanDetails,
		single: true,
		params: {
			filters,
		},
	})
	return (
		<Modal
			open={open}
			onClose={onClose}>
			<Box sx={css.modalContainer}>
				<FlexColumn
					center
					onClick={onClose}
					sx={{
						position: 'absolute',
						top: '10px',
						right: '10px',
						backgroundColor: 'rgba(255,255,255,0.2)',
						width: '24px',
						height: '24px',
						cursor: 'pointer',
						borderRadius: '50%',
						padding: '4px',
					}}>
					<FontAwesomeIcon
						size={'lg'}
						color={'#fff'}
						icon={faXmark}
					/>
				</FlexColumn>
				<FlexColumn>
					<DeviceFrame
						deviceTag={device.value}
						scale={0.75}
						notchColor={branding?.themeColor ?? '#033598'}>
						<WithMobileScroll>
							{data && (
								<WorkoutDetails
									workout={data}
									updateWorkout={update}
									isPreview={true}
								/>
							)}
						</WithMobileScroll>
					</DeviceFrame>
				</FlexColumn>
			</Box>
		</Modal>
	)
}
