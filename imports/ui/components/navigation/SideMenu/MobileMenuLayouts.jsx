import React, { useMemo } from 'react'
import _ from 'lodash'
import MenuDropdownButton from './buttons/MenuDropdownButton.jsx'
import { Collapse } from '@mui/material'
import DefaultButtonLayout from './DefaultButtonLayout.jsx'
import { SubMenuDefaultLayout } from './SideMenu.jsx'
import FlexColumn from '../../layouts/FlexColumn'

export default function MobileMenuLayouts({ variant, ...props }) {
	const Variants = {
		buttons: ButtonsMenuLayout,
	}

	const Component = Variants[variant]

	return <Component {...props} />
}

function ButtonsMenuLayout({ items, controller, outlet }) {
	const { activeItem, setActiveItem, minimized, toggleMinimized } = controller

	const css = {
		dropdown: {
			border: '1px solid #ECF2FD',
			borderRadius: '8px',
			padding: '16px',
			marginTop: '4px',
			boxShadow: 'rgba(0, 0, 0, 0.1) 0px 1px 1px 0px, rgba(0, 0, 0, 0.06) 0px 1px 1px 0px;',
			backgroundColor: '#FFFFFF',
		},
	}

	const flattenedItems = _.flatMap(items, item => {
		if (item.submenu) {
			return _.map(item.submenu, subItem => {
				return {
					...subItem,
					label: item.label + ' / ' + subItem.label,
				}
			})
		}
		return item
	})
	const item = _.find(flattenedItems, { key: activeItem })

	const onClickItem = key => {
		setActiveItem(key)
		toggleMinimized()
	}

	const renderItems = useMemo(() => {
		const components = {
			button: DefaultButtonLayout,
			submenu: SubMenuDefaultLayout,
		}
		return _.map(items, item => {
			const ButtonLayout = item.submenu ? components.submenu : components.button
			return (
				<ButtonLayout
					key={item.key}
					item={item}
					activeItem={activeItem}
					onClick={key => onClickItem(key)}
				/>
			)
		})
	}, [items, activeItem])

	return (
		<FlexColumn
			spacing={2}
			fullWidth>
			<FlexColumn>
				<MenuDropdownButton
					onClick={toggleMinimized}
					isOpen={minimized}>
					{item.label}
				</MenuDropdownButton>
				<FlexColumn
					sx={{ position: 'relative' }}
					fullWidth>
					<FlexColumn
						sx={{ position: 'absolute', zIndex: 10 }}
						fullWidth>
						<Collapse
							in={minimized}
							timeout={300}
							unmountOnExit>
							<FlexColumn
								spacing={2}
								sx={css.dropdown}>
								{renderItems}
							</FlexColumn>
						</Collapse>
					</FlexColumn>
				</FlexColumn>
			</FlexColumn>
			{outlet}
		</FlexColumn>
	)
}
