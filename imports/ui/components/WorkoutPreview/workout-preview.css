.workout-preview-container {
	height: 100%;
	overflow-y: auto;
	position: relative;
	width: 100%;
}

.workout-preview-container .header-preview {
	align-items: center;
	background: white;
	display: inline-flex;
	padding: 1rem;
	position: sticky;
	top: 0;
	width: 100%;
	z-index: 2;
	border-top-left-radius: 30px;
	border-top-right-radius: 30px;
}

.workout-preview-container .header-preview .home-icon {
	height: 40px;
}

.workout-preview-container .header-preview .title-preview {
	font-size: 20px;
	font-weight: bold;
	text-align: center;
}

.workout-preview-container .header-preview .subtitle-preview {
	font-style: normal;
	font-weight: 600;
	font-size: 18px;
	margin-left: 43px;
}

.workout-preview-container .header-preview .home-label {
	color: #1249b7;
	font-style: normal;
	font-weight: 500;
	font-size: 16px;
}

.workout-preview-container .header-preview .user-icon {
	background-color: black;
	border-radius: 50%;
	color: white;
	font-size: 20px;
	padding: 0.5rem;
}

.workout-preview-container .body-preview .notes-container {
}

.workout-preview-container .body-preview .notes-container .workout-notes {
	font-style: normal;
	font-weight: 500;
	font-size: 14px;
}

.workout-preview-container .body-preview .notes-container .notes {
	color: #667085;
	font-style: normal;
	font-weight: normal;
	font-size: 14px;
}

.workout-preview-container .body-preview {
	background: #f6f9fe;
	min-height: 100%;
	padding: 20px;
	margin-bottom: 50px;
}

.workout-preview-container .body-preview .workout-preview-item {
	background-color: white;
	font-size: 17px;
	margin-bottom: 0.5rem;
	padding: 1rem 1.5rem;
	position: relative;
	text-align: center;
	width: 100%;
	border-radius: 16px;
	margin-top: 20px;
}

.workout-preview-container .body-preview .superset-title {
	margin-bottom: 0;
}

.workout-preview-container .body-preview .superset-item {
	border-bottom: 1px solid #919191 !important;
	margin-bottom: 0;
}

.workout-preview-container .body-preview .superset-item:first-child {
	border-top: 1px solid #919191 !important;
}

.workout-preview-container .body-preview .superset-item:last-child {
	border-bottom: none !important;
	margin-bottom: 0.5rem;
}

.workout-preview-container .body-preview .workout-preview-item .video-button {
	background: transparent;
	border: none;
	left: 0;
	padding: 0.5rem;
	position: absolute;
	top: 0;
}

.workout-preview-container .body-preview .workout-preview-item .audio-button {
	background: transparent;
	border: none;
	bottom: 0;
	left: 0;
	padding: 0.5rem;
	position: absolute;
}

.workout-preview-container .body-preview .workout-preview-item img {
	height: 0.7rem;
}

.workout-preview-container .body-preview .empty-label {
	font-style: italic;
	font-weight: 300;
	margin-top: 0.5rem;
	text-align: center;
	text-transform: capitalize;
}

.workout-preview-container .footer-preview {
	background-color: white;
	bottom: 0;
	padding: 1px 0.5px;
	position: fixed;
	width: 100%;
}

.workout-preview-container .container-preview .container-scroll {
	height: 91%;
	overflow-y: auto;
	border-bottom-left-radius: 25px;
	border-bottom-right-radius: 25px;
	background: #f6f9fe;
}
.workout-preview-container .container-preview {
	padding-top: 35px;
	padding-left: 30px;
	padding-right: 30px;
	height: 700px;
	overflow-y: hidden;
	border-radius: 90px;
}

.card-container {
	box-shadow:
		0px 4px 8px -2px rgba(16, 24, 40, 0.1),
		0px 2px 4px -2px rgba(16, 24, 40, 0.06);
	border-radius: 16px;
	background-color: white;
	font-size: 17px;
	margin-bottom: 0.5rem;
	padding: 0;
	position: relative;
	text-align: center;
	width: 100%;
	margin-top: 20px;
}

.header-workout-card {
	width: 100%;
	display: flex;
	align-items: center;
	justify-content: space-between;
	font-size: 14px;
	font-weight: bold;
}

.workoutDetailContent {
	text-align: left;
	font-size: 14px;
	font-weight: normal;
}

/*.css-pwcg7p-MuiCollapse-root {*/
/*    margin-top: -20px;*/
/*}*/

.exercice-detail {
	font-size: 14px;
}

.exercice-detail .exercice {
	margin-left: 5px;
	color: gray;
	font-size: 14px;
}

.disabled-custom {
	background: #ffffff !important;
	opacity: 0.4 !important;
}

.disabled-custom .title-accordion-workout {
	text-decoration: line-through !important;
}

.card-container-superset .header-workout-card span:first-child {
	position: absolute;
	right: 4px;
}

.header-workout-card span:first-child {
	position: absolute;
	right: 4px;
}

.card-container .header-workout-card {
	margin-left: 10px;
}

.notes-exercise-detail {
	text-align: start;
}
.notes-exercise-detail p:first-child {
	padding-top: 17px;
	font-style: normal;
	font-weight: 500;
	font-size: 14px;
	line-height: 20px;
	color: var(--gray-700);
}

.notes-exercise-detail .note {
	font-weight: normal;
	font-size: 14px;
	line-height: 20px;
	color: var(--gray-700);
}

.play-video-detail-workout {
	width: 100%;
	border: 1px solid var(--primary-50) !important;
	font-weight: 500;
	font-size: 14px;
	text-transform: none !important;
	border-radius: 6px !important;
	padding: 0.4rem !important;
	margin-top: 2rem !important;
}

.custom-card {
	height: 100% !important;
}

.notes-item-card {
	font-weight: 500;
	font-size: 14px;
	line-height: 20px;
	color: var(--gray-700);
}

.note-item-card {
	font-weight: 500;
	font-size: 13px;
	line-height: 20px;
	color: var(--gray-500);
}

.isPlayingAudio {
	background-color: var(--primary-600) !important;
	border: 1px solid var(--primary-600) !important;
	color: white !important;
	font-weight: 500;
	width: 100%;
	font-size: 14px;
	text-transform: none !important;
	border-radius: 6px !important;
	padding: 0.4rem !important;
	margin-top: 2rem !important;
}
