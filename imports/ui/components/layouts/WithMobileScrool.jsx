import React, { useEffect, useRef } from 'react'
import Hammer from 'hammerjs'
import Box from '@mui/material/Box'

const WithMobileScroll = ({ children, scrollSpeed = 1 }) => {
	const containerRef = useRef(null)

	useEffect(() => {
		if (!containerRef.current) return

		const container = containerRef.current
		const hammer = new Hammer(container)

		hammer.get('pan').set({ direction: Hammer.DIRECTION_VERTICAL })

		hammer.on('pan', event => {
			container.scrollTop -= event.deltaY * (scrollSpeed * 0.1)
		})

		return () => {
			hammer.destroy()
		}
	}, [scrollSpeed])

	const sx = {
		container: {
			overflow: 'auto',
			height: '100%',
			'&::-webkit-scrollbar': {
				display: 'none',
			},
			'&-ms-overflow-style:': {
				display: 'none', // Hide the scrollbar for IE
			},
			'&::WebkitOverflowScrolling': {
				'-webkit-overflow-scrolling': 'touch',
			},
		},
	}

	return (
		<Box
			ref={containerRef}
			sx={sx.container}>
			{children}
		</Box>
	)
}

export default WithMobileScroll
