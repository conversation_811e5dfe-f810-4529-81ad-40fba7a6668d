import React from 'react'
import Grid from '@mui/material/Grid'
import Card from '@mui/material/Card'
import Typography from '@mui/material/Typography'
import WorkoutDropdown from './WorkoutDropdown'
import Box from '@mui/material/Box'
import Tooltip from '@mui/material/Tooltip'
import moment from 'moment'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faTrashCan } from '@fortawesome/pro-regular-svg-icons'

if (Meteor.isClient) import('./workout-dropdown.css')

const DraftWorkoutCard = ({ optionsCard, draft, onEdit, onDelete, sort }) => {
	const { _id, title, createdAt, updatedAt } = draft

	return (
		<Grid
			item
			xs={12}
			sm={6}
			md={4}
			lg={3}
			xl={2}
			sx={{
				marginRight: { md: 6 },
				display: { xs: 'flex' },
				justifyContent: { sm: 'center', xs: 'center' },
				marginTop: 4,
				cursor: 'pointer',
			}}>
			<Card
				sx={{
					height: 120,
					width: 320,
					cursor: 'pointer',
					boxShadow: 'none',
					border: '1px solid var(--gray-300)',
					borderRadius: 2,
				}}>
				<Grid
					container
					sx={{ height: '100%' }}>
					<Grid
						item
						xs={10}
						onClick={() => onEdit(_id)}
						sx={{
							display: 'flex',
							flexDirection: 'column',
							alignItems: 'flex-start',
							justifyContent: 'space-between',
						}}>
						<Tooltip title={title}>
							<Typography
								variant="h6"
								component="p"
								sx={{
									textOverflow: 'ellipsis',
									overflow: 'hidden',
									whiteSpace: 'nowrap',
									width: '100%',
									fontWeight: '500',
									color: title === 'Untitled' ? 'var(--gray-700)' : 'var(--gray-900)',
								}}>
								{title}
							</Typography>
						</Tooltip>
						<Typography color="text.secondary">{`${draft?.details ?? 0} Tasks`}</Typography>
						<Typography
							variant={'caption'}
							color="text.secondary">
							{sort === 'CreatedAsd' && `Created ${moment(createdAt).fromNow()}`}
							{sort === 'UpdatedAsd' &&
								(updatedAt
									? `Last updated ${moment(updatedAt).fromNow()}`
									: 'Last updated a long time ago')}
						</Typography>
					</Grid>
					<Grid
						item
						xs={2}>
						<Box
							display={'flex'}
							sx={{ gap: '10px', justifyContent: 'flex-end' }}
							className="items-center">
							<FontAwesomeIcon
								icon={faTrashCan}
								onClick={() => onDelete(draft)}
							/>
							<WorkoutDropdown
								id={_id}
								options={optionsCard}
								isCustomSort={sort === 'Custom'}
							/>
						</Box>
					</Grid>
				</Grid>
			</Card>
		</Grid>
	)
}

export default DraftWorkoutCard
