import React, { Fragment, useEffect, useState } from 'react'
import Stack from '@mui/material/Stack'
import Box from '@mui/material/Box'
import { faSlidersSimple } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import Typography from '@mui/material/Typography'
import Popover from '@mui/material/Popover'
import FormLabel from '@mui/material/FormLabel'
import FormControl from '@mui/material/FormControl'
import RadioGroup from '@mui/material/RadioGroup'
import FormControlLabel from '@mui/material/FormControlLabel'
import Radio from '@mui/material/Radio'
import { useList } from '@hooks/data/useList'
import useEventHandler from '@hooks/data/useEventHandler'
import { capitalize } from 'lodash'

export default function FiltersBar({ categories, onChangeCategory, onChangeTags }) {
	const [selectedCategory, setSelectedCategory] = useState(categories[0])
	const [anchorEl, setAnchorEl] = useState(null)
	const selectedTags = useList([])

	useEffect(() => {
		if (categories.length > 0) {
			setSelectedCategory(categories[0])
		}
	}, [categories])

	const handleClick = event => {
		setAnchorEl(event.currentTarget)
	}

	const handleClose = () => {
		setAnchorEl(null)
	}

	const open = Boolean(anchorEl)
	const id = open ? 'simple-popover' : undefined

	const handleChangeCategory = useEventHandler(({ value, clearEvent }) => {
		clearEvent()
		if (value === 'all') {
			setSelectedCategory({
				value: 'all',
				label: 'All',
			})
			onChangeCategory({
				value: 'all',
				label: 'All',
			})
			selectedTags.reset()
			onChangeTags([])
			return
		}
		const category = categories.find(category => category.value === value)
		if (category) {
			setSelectedCategory(category)
			onChangeCategory(category)
			selectedTags.reset()
			onChangeTags([])
		}
	})

	const toggleTag = value => {
		if (selectedTags.includes(value)) {
			selectedTags.remove(value)
			onChangeTags(selectedTags.items.filter(item => item !== value))
			return
		}
		selectedTags.add(value)
		onChangeTags([...selectedTags.items, value])
	}

	return (
		<Stack
			direction="row"
			alignItems="center"
			sx={{
				gap: 2,
			}}>
			<Box
				onClick={handleClick}
				sx={{
					backgroundColor: open ? 'var(--primary-100)' : 'var(--gray-200)',
					borderRadius: 4,
					padding: '4px 12px',
					display: 'flex',
					alignItems: 'center',
					justifyContent: 'center',
					gap: 1,
					cursor: 'pointer',
				}}>
				<FontAwesomeIcon
					icon={faSlidersSimple}
					color="var(--gray-700)"
				/>
				<Typography variant="caption">{selectedCategory ? selectedCategory.label : 'All'}</Typography>
			</Box>
			{id && (
				<Popover
					id={id}
					open={open}
					anchorEl={anchorEl}
					onClose={handleClose}
					elevation={2}
					anchorOrigin={{
						vertical: 'bottom',
						horizontal: 'left',
					}}
					sx={{
						'& .MuiPopover-paper': {
							borderRadius: 3,
							backgroundColor: 'white',
							boxShadow: 'rgba(50, 50, 93, 0.25) 0px 2px 5px -1px, rgba(0, 0, 0, 0.3) 0px 1px 3px -1px',
							p: 3,
						},
						mt: 1,
					}}>
					<FormControl>
						<FormLabel
							sx={{
								'$.MuiFormLabel-root': {
									color: 'var(--gray-700)',
								},
							}}>
							Filter
						</FormLabel>
						<RadioGroup
							defaultValue={selectedCategory.value}
							onChange={handleChangeCategory}>
							<FormControlLabel
								value={'all'}
								control={<Radio />}
								label={'All'}
							/>
							{categories.map((category, index) => (
								<FormControlLabel
									key={index}
									value={category.value}
									control={<Radio />}
									label={category.label}
								/>
							))}
						</RadioGroup>
					</FormControl>
				</Popover>
			)}
			{selectedCategory === 'All' &&
				categories.map((category, index) => (
					<Fragment key={index}>
						{category.options.map((option, index) => (
							<Box
								key={index}
								sx={{
									border: '1px solid var(--gray-200)',
									borderRadius: '8px',
									padding: '4px 12px',
									display: 'flex',
									alignItems: 'center',
									justifyContent: 'center',
									gap: '1rem',
									cursor: 'pointer',
									'&:hover': {
										backgroundColor: 'var(--gray-100)',
										border: '1px solid var(--gray-300)',
									},
								}}
								onClick={() => {
									toggleTag(option[0])
								}}>
								{option[0]} ({option[1]})
							</Box>
						))}
					</Fragment>
				))}

			{selectedCategory?.options &&
				selectedCategory.options.map((option, index) => (
					<Box
						key={index}
						onClick={() => toggleTag(option[0])}
						sx={{
							backgroundColor: selectedTags.includes(option[0])
								? 'var(--primary-100)'
								: 'var(--gray-100)',
							border:
								'1px solid ' +
								(selectedTags.includes(option[0]) ? 'var(--primary-300)' : 'var(--gray-300)'),
							borderRadius: 4,
							padding: '4px 12px',
							display: 'flex',
							alignItems: 'center',
							justifyContent: 'center',
							gap: 1,
							cursor: 'pointer',
						}}>
						<Typography
							variant="caption"
							sx={{
								color: selectedTags.includes(option[0]) ? 'var(--primary-700)' : 'var(--gray-700)',
							}}>
							{capitalize(option[0])} ({option[1]})
						</Typography>
					</Box>
				))}
		</Stack>
	)
}
