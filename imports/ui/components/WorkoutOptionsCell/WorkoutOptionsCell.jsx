import React, { Fragment, useRef, useState } from 'react'
import Tooltip from '@mui/material/Tooltip'
import MoreVertIcon from '@mui/icons-material/MoreVert'
import notifyError from '../../utility/toast/notifyError'
import { setChangeDetailListValue, setChangeRowDetail } from '../../../api/states/workoutPageState'
import Popover from '@mui/material/Popover'
import { GrayConfigBtn } from '../../utility/buttons/BackButton'
import ConfirmModal from '../ConfirmModal/ConfirmModal'
import getCloudinaryImage from '../cloudinary/getCloudinaryImage'
import WorkoutDetailPropertiesModal from '../WorkoutConfigPropertiesModal/WorkoutDetailPropertiesModal'

const WorkoutOptionsCell = ({ item, details, superset, exercises, onChangeRow }) => {
	const [anchorEl, setAnchorEl] = useState(null)
	const [openConfirmationModal, setOpenConfirmationModal] = useState(false)
	const [showPropertiesModal, setShowPropertiesModal] = useState(false)

	const handleOptions = event => setAnchorEl(event.currentTarget)

	const handleClose = () => setAnchorEl(null)

	const open = Boolean(anchorEl)
	const id = open ? `${item?._id}-popover` : undefined

	const onHandleConfirmationModal = () => setOpenConfirmationModal(!openConfirmationModal)

	const onConfirm = () => {
		if (superset) {
			if (details.length > 1) {
				removeDetail(item._id, { reloadSuperSet: true })
			} else {
				removeDetailAndSuperset(item._id, superset._id)
			}
		} else {
			removeDetail(item._id)
		}
		setOpenConfirmationModal(!openConfirmationModal)
	}

	const removeDetail = (id, reloadSuperSet) => {
		Meteor.callAsync('workoutDetail.remove', { _id: id })
			.then(result => {
				if (reloadSuperSet) {
					setChangeDetailListValue()
				}
				setChangeRowDetail()
				onChangeRow()
			})
			.catch(error => {
				notifyError(error.reason)
			})
	}

	const removeDetailAndSuperset = (detailId, supersetId) => {
		Meteor.callAsync('workoutDetail.remove', { _id: detailId })
			.then(() => {
				return Meteor.callAsync('workoutDetail.remove', {
					_id: supersetId,
				})
			})
			.then(() => {
				onChangeRow()
				setChangeDetailListValue()
			})
			.catch(error => {
				notifyError(error.reason)
			})
	}

	const transformToSuperset = item => onSuperSetTransform({ workoutDetailForUpdate: item })

	const btnRef = useRef()
	const onSuperSetTransform = workoutDetail => {
		if (btnRef.current) {
			btnRef.current.setAttribute('disabled', 'disabled')
		}

		const payloadUpdate = {
			_id: workoutDetail?.workoutDetailForUpdate?._id,
			modifier: {
				$set: {
					workoutId: workoutDetail?.workoutDetailForUpdate?.workoutId,
					order: workoutDetail?.workoutDetailForUpdate?.order,
					suborder: 100000,
				},
			},
		}

		const newExcercice = {
			order: workoutDetail?.workoutDetailForUpdate?.order,
			workoutId: workoutDetail?.workoutDetailForUpdate?.workoutId,
			superset: 'true',
			suborder: 0,
		}
		if (workoutDetail?.workoutDetailForUpdate?.part) {
			updateWorkoutDetail(payloadUpdate)
		} else {
			updateWorkoutDetail(payloadUpdate)
		}
		insertWorkoutDetail(newExcercice)
	}

	const upsertWorkoutDetailHeadingToSuperset = detail => {
		Meteor.callAsync('workoutDetail.changeSection', detail).catch(error => {
			notifyError(error.reason)
		})
	}

	const upsertWorkoutDetailChangeExercise = detail => {
		Meteor.callAsync('workoutDetail.changeExercise', detail).catch(error => {
			notifyError(error.reason)
		})
	}

	const insertWorkoutDetail = detail => {
		Meteor.callAsync('workoutDetail.insert3', detail)
			.then(() => {
				onChangeRow()
				setChangeDetailListValue()
			})
			.catch(error => {
				notifyError(error.reason)
			})
	}

	const updateWorkoutDetail = payload => {
		Meteor.callAsync('workoutDetails.update', payload)
			.then(() => {
				onChangeRow()
				setChangeDetailListValue()
			})
			.catch(error => {
				notifyError(error.reason)
			})
	}

	const onTransformToSection = item => transformToSection({ workoutDetailForUpdate: item })

	const transformToSection = workoutDetail => {
		let section = ' '
		if (workoutDetail?.workoutDetailForUpdate?.movement) {
			section = workoutDetail?.workoutDetailForUpdate?.movement
		}
		const forUpdate = {
			_id: workoutDetail?.workoutDetailForUpdate?._id,
			part: section,
			workoutId: workoutDetail?.workoutDetailForUpdate?.workoutId,
		}
		upsertWorkoutDetailChangeExercise(forUpdate)
		onChangeRow()
	}

	const exerciseTransform = workoutDetail => {
		const data = {
			_id: workoutDetail._id,
			workoutId: workoutDetail?.workoutId,
			movement: workoutDetail?.part,
			order: workoutDetail?.order,
		}
		upsertWorkoutDetailHeadingToSuperset(data)
		onChangeRow()
	}

	return (
		<>
			<article className="inline-flex items-center">
				<Tooltip title="">
					<MoreVertIcon
						className="table-icon-actions"
						onClick={handleOptions}
					/>
				</Tooltip>
				{!item.hasOwnProperty('part') && (
					<Tooltip title="Edit properties">
						<img
							src={getCloudinaryImage('resources/images/edit_icon').toURL()}
							alt="edit"
							className="table-icon-actions"
							onClick={() => setShowPropertiesModal(true)}
						/>
					</Tooltip>
				)}
				<Tooltip title="Remove">
					<img
						src={getCloudinaryImage('resources/images/trash_icon').toURL()}
						alt="remove"
						className="table-icon-actions"
						onClick={onHandleConfirmationModal}
					/>
				</Tooltip>
			</article>

			<Popover
				id={id}
				open={open}
				anchorEl={anchorEl}
				onClose={handleClose}
				anchorOrigin={{
					vertical: 'bottom',
					horizontal: 'left',
				}}
				PaperProps={{
					style: {
						border: '1px solid var(--gray-200)',
						boxShadow: '0px 12px 16px -4px rgba(16, 24, 40, 0.1), 0px 4px 6px -2px rgba(16, 24, 40, 0.05)',
						borderRadius: '8px',
						padding: '16px',
						width: '200px',
					},
				}}>
				{(!item.hasOwnProperty('suborder') || item?.suborder === null) && (
					<GrayConfigBtn
						sx={{ marginBottom: '10px' }}
						onClick={() => transformToSuperset(item)}
						ref={btnRef}>
						<img
							src={getCloudinaryImage('resources/images/link_icon').toURL()}
							alt="superset"
							className="table-icon-actions"
						/>{' '}
						Create Superset
					</GrayConfigBtn>
				)}

				{!item.hasOwnProperty('part') && (
					<GrayConfigBtn onClick={() => onTransformToSection(item)}>
						<img
							src={getCloudinaryImage('resources/images/heading_icon').toURL()}
							alt="heading"
							className="table-icon-actions"
						/>{' '}
						Create Heading
					</GrayConfigBtn>
				)}

				{item.hasOwnProperty('part') && (
					<GrayConfigBtn onClick={() => exerciseTransform(item)}>
						<img
							src={getCloudinaryImage('resources/images/undo_icon').toURL()}
							alt="heading"
							className="table-icon-actions"
						/>{' '}
						Revert to Row
					</GrayConfigBtn>
				)}
			</Popover>

			{openConfirmationModal && (
				<ConfirmModal
					open={openConfirmationModal}
					handleClose={onHandleConfirmationModal}
					onConfirm={onConfirm}
					message="Are you sure you want to delete this row?"
				/>
			)}
			{showPropertiesModal && (
				<WorkoutDetailPropertiesModal
					open={showPropertiesModal}
					workoutDetail={item}
					updateDetail={() => {
						setChangeRowDetail()
						onChangeRow()
					}}
					onClose={() => setShowPropertiesModal(false)}
					settings={{
						mediaType: 'video',
						enableVideo: true,
						enableImages: true,
						enableNotes: true,
						enableAudio: true,
					}}
				/>
			)}
		</>
	)
}

export default WorkoutOptionsCell
