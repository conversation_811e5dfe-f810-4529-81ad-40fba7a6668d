import React from 'react'
import Box from '@mui/material/Box'
import Modal from '@mui/material/Modal'
import { ContainedRedBtn, OutlineWhiteBtn } from '../../utility/buttons/BackButton'

const style = {
	position: 'absolute',
	top: '50%',
	left: '50%',
	transform: 'translate(-50%, -50%)',
	width: 400,
	bgcolor: 'background.paper',
	border: 'none',
	boxShadow: 24,
	borderRadius: 3,
	padding: 4,
	'& h6': {
		color: 'var(--gray-700)',
		fontSize: '16px',
		lineHeight: '24px',
		marginBottom: '25px',
		textTransform: 'none',
	},
	'&:active': {
		border: 'none',
	},
}

const ConfirmModal = ({ open, handleClose, onConfirm, message = 'Are you sure you want to delete this?' }) => {
	return (
		<Modal
			open={open}
			onClose={handleClose}
			aria-labelledby="modal-modal-title"
			aria-describedby="modal-modal-description">
			<Box sx={style}>
				<h6 className="uppercase">{message}</h6>

				<ContainedRedBtn
					sx={{
						marginBottom: '8px',
						padding: '8px',
						width: '100%',
					}}
					onClick={onConfirm}>
					Delete
				</ContainedRedBtn>
				<OutlineWhiteBtn
					sx={{
						padding: '8px',
						width: '100%',
					}}
					onClick={handleClose}>
					Cancel
				</OutlineWhiteBtn>
			</Box>
		</Modal>
	)
}

export default ConfirmModal
