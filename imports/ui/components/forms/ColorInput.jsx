import React from 'react'
import { Button, Typography } from '@mui/material'
import FlexColumn from '../layouts/FlexColumn'
import styled from '@emotion/styled'
import FlexRow from '../layouts/FlexRow'
import { faEllipsisVertical } from '@fortawesome/pro-regular-svg-icons'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'

const StyledInput = styled('input')(
	{
		background: 'transparent',
		border: '1px solid var(--gray-300)',
		boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
		borderRadius: '8px',
		color: 'var(--gray-500)',
		fontSize: '16px',
		fontWeight: 400,
		lineHeight: '24px',
		height: '44px',
		width: '48px',
		padding: '4px',
		transition: 'all 200ms ease',
		'&:hover': {
			outline: 'none',
		},
		'&:focus': {
			outline: 'none',
		},
		'&.invalid': {
			border: '1px solid var(--red-600)',
		},
	},
	({ readOnly }) => ({ cursor: readOnly ? 'block' : 'pointer' }),
)

export default function ColorInput({
	label,
	fullWidth,
	widths,
	value,
	onChange,
	onBlur,
	onOptionsClick,
	readOnly,
	...props
}) {
	const css = {
		textField: {
			borderRadius: onOptionsClick ? '8px 0 0 8px' : '8px',
			height: '44px',
			width: '100%',
			border: '1px solid var(--gray-300)',
			padding: '0 12px',
		},
		optionsButton: {
			borderRadius: '0 8px 8px 0',
			border: '1px solid var(--gray-300)',
			borderLeft: 'none',
			height: '44px',
			width: '22px!important',
			padding: '0 12px',
			textTransform: 'none',
			'&:hover': {
				backgroundColor: 'var(--gray-100)',
				borderColor: 'var(--gray-300)',
				borderLeft: 'none',
			},
		},
	}

	const handleColorChange = e => {
		const hexCharPattern = '[0-9A-Fa-f]+'
		if (e.target.value.slice(-1).match(hexCharPattern)) {
			onChange(e)
		}
	}

	return (
		<FlexColumn
			fullWidth={fullWidth}
			widths={widths}
			{...props}>
			<Typography variant={'caption'}>{label}</Typography>
			<FlexRow gap={1}>
				<StyledInput
					type={'color'}
					value={value}
					onChange={onChange || (() => {})}
					readOnly={readOnly}
					onBlur={onBlur}
				/>
				<FlexRow fullWidth>
					<input
						maxLength={7}
						style={css.textField}
						value={value}
						onChange={handleColorChange}
						readOnly={readOnly}
					/>
					{onOptionsClick && (
						<Button
							sx={css.optionsButton}
							variant={'outlined'}
							onClick={onOptionsClick}>
							<FontAwesomeIcon icon={faEllipsisVertical} />
						</Button>
					)}
				</FlexRow>
			</FlexRow>
		</FlexColumn>
	)
}
