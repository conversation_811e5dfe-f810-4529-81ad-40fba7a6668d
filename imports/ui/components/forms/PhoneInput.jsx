import React, { useEffect } from 'react'
import { Typography } from '@mui/material'
import { Meteor } from 'meteor/meteor'
import MuiPhoneNumber from 'dyxbenjamin-mui-phone-number'
import FlexColumn from '../layouts/FlexColumn'
import { parsePhoneNumber } from 'react-phone-number-input'

const css = {
	MuiPhoneNumber: {
		'&.MuiFormControl-root': {
			background: 'white',
			border: '1px solid var(--gray-300) !important',
			boxShadow: '0px 1px 2px rgba(16, 24, 40, 0.05)',
			borderRadius: '8px',
			color: 'var(--gray-500)',
			fontSize: '16px',
			fontWeight: 'normal',
			lineHeight: '24px',
			margin: 0,
			width: '100%',
			minHeight: '44px',
			padding: '10px 0',
			transition: 'all 200ms ease',
		},
		'& .MuiOutlinedInput-multiline': {
			padding: '0 !important',
		},
		'& .MuiInputLabel-root': {
			display: 'none',
		},
		'&.Mui-focused': {
			border: 'none !important',
		},
		'&:focus': {
			outline: 'none',
		},
		'&:active': {
			outline: 'none',
		},
		'& input::placeholder': {
			color: 'var(--gray-500)',
		},
		'& input': {
			padding: 0,
		},
		'& textarea': {
			padding: 0,
		},
		'& fieldset': {
			border: 'none',
		},
	},
}
export default function PhoneInput({ label, fullWidth, widths, defaultValue, onChange, onBlur, ...props }) {
	const [value, setValue] = React.useState(defaultValue)
	useEffect(() => {
		setValue(defaultValue)
	}, [defaultValue])

	const handleChange = value => {
		const payload = parsePhoneNumber(value)?.number || ''
		onChange(payload)
	}

	return (
		<FlexColumn
			fullWidth={fullWidth}
			widths={widths}
			{...props}>
			<Typography variant={'caption'}>{label}</Typography>
			<MuiPhoneNumber
				defaultCountry="us"
				onBlur={onBlur}
				value={value}
				sx={css.MuiPhoneNumber}
				variant={'outlined'}
				className="testing"
				disableDropdown={Meteor.isServer}
				name="phone"
				onChange={handleChange}
				native
				disableAreaCodes
			/>
		</FlexColumn>
	)
}
