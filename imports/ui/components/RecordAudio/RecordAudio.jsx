import React, { Fragment } from 'react'
import MicRecorder from 'mic-recorder-to-mp3'
import notifyError from './../../utility/toast/notifyError'
import { PrimaryContainedBtn } from '../../utility/buttons/BackButton'
import AudioStepsEnum from './AudioSteps.enum'
import getCloudinaryImage from '../cloudinary/getCloudinaryImage'
import { uploadCloudinaryFile } from '../../../api/cloudinary/CloudinaryMethods'

const Mp3Recorder = new MicRecorder({ bitRate: 128 })

const RecordAudio = ({
	workoutId,
	workoutDetailId,
	actionType,
	blocked,
	handleClose,
	onActionType,
	audioNoteFeedback,
	coachId,
	clientId,
	temporaryCommentId,
}) => {
	const startRecordAudio = () => {
		if (blocked) {
			notifyError('Permission denied', {
				position: 'top-center',
				autoClose: 3000,
				hideProgressBar: false,
				closeOnClick: true,
				pauseOnHover: false,
				draggable: true,
				progress: undefined,
			})
		} else {
			Mp3Recorder.start()
				.then(() => onActionType(AudioStepsEnum.RECORDING))
				.catch(error => {
					notifyError('Recording could not be done', {
						position: 'top-center',
						autoClose: 3000,
						hideProgressBar: false,
						closeOnClick: true,
						pauseOnHover: false,
						draggable: true,
						progress: undefined,
					})
				})
		}
	}

	const stopRecordAudio = () => {
		Mp3Recorder.stop()
			.getMp3()
			.then(([buffer, blob]) => {
				const blobURL = URL.createObjectURL(blob)
				uploadCloudinary(blob)
				onActionType(AudioStepsEnum.PLAY)
			})
			.catch(e => {
				onActionType(AudioStepsEnum.PLAY)
			})
	}

	const uploadCloudinary = file => {
		Meteor.call(
			'getCloudinarySignature',
			{
				options: {
					folder: `W-${workoutId}/D-${workoutDetailId ?? 'comment'}`,
					timestamp: Date.now(),
					// upload_preset: Meteor.settings.public.cloudinary.upload_presets.default,
					// source: 'u'
				},
			},
			(error, parametersSigned) => {
				const payload = {
					folder: `W-${workoutId}/D-${workoutDetailId ?? 'comment'}`,
					timestamp: parametersSigned.uploadSignatureTimestamp,
					...parametersSigned,
				}
				uploadCloudinaryFile(file, payload)
					.then(response => {
						updateAudioRecord(response)
					})
					.catch(error => {
						console.log('CLOUDINARY ERROR', error)
						notifyError(error.error.message)
					})
			},
		)
	}

	const updateAudioRecord = cloudinaryResponse => {
		const urlAudio = cloudinaryResponse?.secure_url
		const cloudinaryAudioPublicId = cloudinaryResponse?.public_id
		if (audioNoteFeedback === true) {
			if (temporaryCommentId) {
				const data = {
					_id: temporaryCommentId,
					workoutId: workoutId,
					urlAudio: urlAudio,
					coachId: coachId,
					clientId: clientId,
					read: false,
					source: 'CLIENT',
					cloudinaryAudioPublicId,
				}
				Meteor.callAsync('workoutsUserCoachComments.upsert', data)
					.then(res => {
						handleClose({
							urlAudio: urlAudio,
							commentId: res?.insertedId,
						})
					})
					.catch(error => {
						console.log(error)
						notifyError('Error on save audio record')
					})
			} else {
				const data = {
					workoutId: workoutId,
					urlAudio: urlAudio,
					coachId: coachId,
					clientId: clientId,
					read: false,
					source: 'CLIENT',
					cloudinaryAudioPublicId,
				}
				Meteor.callAsync('WorkoutsUserCoachComments.insert', data)
					.then(res => {
						handleClose({ urlAudio: urlAudio, commentId: res })
					})
					.catch(error => {
						console.log(error)
						notifyError('Error on save audio record')
					})
			}
		} else {
			const payload = {
				_id: workoutDetailId,
				modifier: {
					$set: { workoutId, urlAudio: urlAudio },
				},
			}
			Meteor.callAsync('workoutDetails.update', payload)
				.then(response => {
					handleClose({ urlAudio: urlAudio, cloudinaryAudioPublicId })
				})
				.catch(error => {
					console.log(error)
					notifyError('Error on save audio record')
				})
		}
	}

	const onRecordAudio = () => {
		if (actionType === AudioStepsEnum.RECORD) {
			startRecordAudio()
		}
		if (actionType === AudioStepsEnum.RECORDING) {
			stopRecordAudio()
		}
	}

	return (
		<Fragment>
			<PrimaryContainedBtn
				fullWidth
				onClick={onRecordAudio}>
				<img
					src={
						actionType === 'RECORD'
							? getCloudinaryImage('resources/images/record_icon').toURL()
							: getCloudinaryImage('resources/images/recording_icon').toURL()
					}
				/>
				{actionType === 'RECORD'
					? 'Record audio note'
					: actionType === 'RECORDING'
						? 'Recording... Click again to finish'
						: ''}
			</PrimaryContainedBtn>
		</Fragment>
	)
}

export default RecordAudio
