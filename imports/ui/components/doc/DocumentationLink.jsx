import IconButton from '@mui/material/IconButton'
import HelpOutlineIcon from '@mui/icons-material/HelpOutline'
import Tooltip from '@mui/material/Tooltip'
import React from 'react'

const css = {
	tooltip: {
		fontSize: '1.2rem',
	},
	icon: {
		cursor: 'pointer',
		color: 'var(--primary-600)',
		fontSize: '1.4rem',
	},
}
const DocumentationLink = ({ docs }) => {
	if (!docs) return null
	const { url, label } = docs
	const handleClick = () => {
		window.open(url, '_blank')
	}
	return (
		<Tooltip
			title={label}
			placement="bottom"
			arrow
			sx={css.tooltip}
			onClick={handleClick}>
			<IconButton onClick={handleClick}>
				<HelpOutlineIcon sx={css.icon} />
			</IconButton>
		</Tooltip>
	)
}

export default DocumentationLink
