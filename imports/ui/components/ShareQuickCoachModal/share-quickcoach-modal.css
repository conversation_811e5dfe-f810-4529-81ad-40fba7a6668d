.share-quickcoach-modal {
	background-color: white;
	border: none;
	box-shadow:
		0 11px 15px -7px rgba(0 0 0 0.2),
		0 24px 38px 3px rgba(0 0 0, 0.14),
		0 9px 46px 8px rgba(0 0 0 0.12);
	left: 50%;
	min-width: 300px;
	max-width: 500px;
	padding: 30px;
	position: absolute;
	top: 50%;
	transform: translate(-50%, -50%);
	width: 75%;
}

.video-player-modal .video-responsive {
	width: 100% !important;
}

.share-quickcoach-modal .share-content {
	text-align: center;
	margin-top: 40px;
	font-size: 16px;
	color: #667085;
}

.share-quickcoach-modal .share-tittle .close-btn {
	text-align: end;
}
.share-quickcoach-modal .share-tittle .close-btn CloseIcon {
	cursor: pointer;
}

.share-quickcoach-modal .share-buttons Button {
	background: #ffffff;
	border: 1px solid #d0d5dd;
	box-sizing: border-box;
	box-shadow: 0 1px 2px rgba(16, 24, 40, 0.05);
	border-radius: 8px;
	color: #344054;
}

.share-quickcoach-modal .share-buttons {
	display: flex;
	justify-content: space-around;
	margin-top: 30px;
}

.share-quickcoach-modal .share-tittle {
	text-align: center;
	font-weight: 500;
}
