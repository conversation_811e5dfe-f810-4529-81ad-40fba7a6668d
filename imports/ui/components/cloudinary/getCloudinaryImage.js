import Cloudinary from '../../../startup/services/Cloudinary'
/**
 * @deprecated Esta función será eliminada en futuras versiones.
 */
export default value => {
	const cld = Cloudinary
	if (value) {
		const values = value.split(',')
		const public_id = values[0]
		const version = values[1]
		const image = cld.image(public_id)
		if (!version) {
			return image
		}
		return image.setVersion(version)
	}
	return cld.image()
}
