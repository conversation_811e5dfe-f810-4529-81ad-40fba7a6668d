import getCloudinaryImage from '../cloudinary/getCloudinaryImage'
import React, { useEffect, useState } from 'react'
import styled from 'styled-components'
import InstallPwaButton from './InstallPwaButton'
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import Button from '@mui/material/Button'
import InstallDesktopIcon from '@mui/icons-material/InstallDesktop'
import { toast } from 'react-toastify'

const AppIconLayout = styled.div`
	display: flex;
	flex-direction: column;
	gap: 0.2em;
	align-items: center;
	flex-shrink: 0;
`

const InstallToast = ({ appConfigsState }) => {
	const [installPrompt, setInstallPrompt] = useState(null)

	useEffect(() => {
		const handler = e => {
			e.preventDefault()
			setInstallPrompt(e)
		}
		window.addEventListener('beforeinstallprompt', handler)

		return () => window.removeEventListener('beforeinstallprompt', handler)
	}, [])

	const url = window.location.href
	if (!url.includes('/pt/')) appConfigsState = null

	const onDismiss = () => {
		localStorage.setItem('viewedChromeInstallMsg', 'true')
		toast.dismiss()
	}

	return (
		<div
			style={{
				display: 'flex',
				margin: '0.5em 0',
				gap: '1em',
				alignItems: 'start',
			}}>
			<AppIconLayout>
				<img
					src={appConfigsState?.iconUrl ?? getCloudinaryImage('resources/brand/main-logo-512').toURL()}
					style={{
						cursor: 'pointer',
						height: '50px',
						width: '50px',
						borderRadius: '10px',
						boxShadow: '0 0 10px #ddd',
					}}
					alt={'logo-icon'}
				/>
			</AppIconLayout>
			<Box sx={{ display: 'flex', flexDirection: 'column', gap: '15px' }}>
				{installPrompt !== null ? (
					<Typography variant={'subtitle1'}>
						Tap here to install the {appConfigsState?.appName ?? 'QuickCoach'} app.
					</Typography>
				) : (
					<Typography variant={'subtitle1'}>
						Click the icon <InstallDesktopIcon sx={{ color: '#022A7A' }} /> on the address bar to install
						the {appConfigsState?.appName ?? 'QuickCoach'} app.
					</Typography>
				)}
				<Box
					sx={{
						display: 'flex',
						justifyContent: 'space-between',
						gap: '15px',
					}}>
					{installPrompt !== null ? <InstallPwaButton installPrompt={installPrompt} /> : <Box></Box>}
					<Button onClick={onDismiss}>Dismiss</Button>
				</Box>
			</Box>
		</div>
	)
}

export default InstallToast
