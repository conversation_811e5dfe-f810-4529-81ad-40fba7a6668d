import React, { useEffect, useRef, useState } from 'react'
import TableContainer from '@mui/material/TableContainer'
import Paper from '@mui/material/Paper'
import { WorkoutsSupersetCustomTable } from '../utility/tables/CustomTables'
import WorkoutDetailsTableHeader from './WorkoutDetailsTableHeader'
import { ReactSortable } from 'react-sortablejs'
import useStaticGrapher2 from '../hooks/useStaticGrapher2'
import { getWorkoutDetails } from '../../api/workoutDetails/workoutDetails_Q'
import {
	changeDetailListValue,
	changeExerciseValue,
	changeReminderValue,
	hideLoading,
	setChangeDetailListValue,
	setChangeRowDetail,
	showLoading,
} from '../../api/states/workoutPageState'
import WorkoutDetailsTableRow from './WorkoutDetailsTableRow'
import _ from 'lodash'
import TableRow from '@mui/material/TableRow'
import Button from '@mui/material/Button'
import TableCell from '@mui/material/TableCell'
import TableFooter from '@mui/material/TableFooter'
import { getFieldsEnabled } from '../utility/WorkoutUtils/WorkoutUtils'
import notifyError from '../utility/toast/notifyError'
import { useEntity } from 'simpler-state'
import { CustomTableBodyComponent } from '../utility/WorkoutUtils/TableCustomDragBody'
import { getExercises } from '../../api/exercises/exercises_Q'
import { getCoachConfigurationReminder } from '../../api/coachConfiguration/coachConfiguration_Q'
import SupersetGroup from './SupersetGroup'

if (Meteor.isClient) {
	import('./workout-details-table.css')
}

const WorkoutDetailsTable = ({ workout }) => {
	const [workoutsDetailsList, setWorkoutsDetailsList] = useState([])
	const changeDetailsList = useEntity(changeDetailListValue)
	const changeExercises = useEntity(changeExerciseValue)
	const changeReminder = useEntity(changeReminderValue)

	// ━━━━━━━━━━━━━━━━━━ Fetch data ━━━━━━━━━━━━━━━━━━ //
	const { data, ready } = useStaticGrapher2({
		getQuery: getWorkoutDetails,
		singleton: false,
		refresh: false,
		settings: {
			filters: { workoutId: workout._id },
		},
		debug: false,
		inputs: [changeDetailsList],
	})

	useEffect(() => {
		if (ready) {
			setWorkoutsDetailsList(generateSupersetGroups())
		}
	}, [data, ready])

	const { data: exercises } = useStaticGrapher2({
		getQuery: getExercises,
		singleton: false,
		refresh: false,
		settings: {
			filters: {
				userId: Meteor.userId(),
				activate: true,
			},
		},
		debug: false,
		inputs: [changeExercises],
	})

	const { data: coachConfiguration } = useStaticGrapher2({
		getQuery: getCoachConfigurationReminder,
		singleton: false,
		refresh: false,
		settings: {
			filters: {
				userId: Meteor.userId(),
			},
		},
		debug: false,
		inputs: [changeReminder],
	})

	// ━━━━━━━━━━━━━━━━━━ Generate supersets ━━━━━━━━━━━━━━━━━━ //
	const generateSupersetGroups = () => {
		const workoutDetails = data
		const parents = _.filter(
			workoutDetails,
			detail => detail['suborder'] !== null && detail.hasOwnProperty('superset'),
		)
		const children = _.filter(
			workoutDetails,
			detail => detail['suborder'] !== null && !detail.hasOwnProperty('superset'),
		)
		const adults = _.filter(workoutDetails, detail => detail['suborder'] == null)
		const families = _.map(parents, fam => {
			const kids = _.filter(children, child => child.order === fam.order)
			const items = _.orderBy(kids, 'suborder')
			return _.assign(_.cloneDeep(fam), { items })
		})
		return _.orderBy(families.concat(adults), 'order')
	}

	// ━━━━━━━━━━━━━━━━━━ Generate new details ━━━━━━━━━━━━━━━━━━ //
	const btnRef = useRef()

	const addDetail = () => {
		if (btnRef.current) {
			btnRef.current.disabled = true
		}
		showLoading()

		const payload = {
			workoutId: workout._id,
			text: '[]',
			movement: '',
			reps: '',
			sets: '',
			notes: '',
			order: calculateNextPosition(),
		}

		Meteor.callAsync('workoutDetail.insert2', payload)
			.then(() => {
				setChangeDetailListValue()
				hideLoading()
				Meteor.setTimeout(() => (btnRef.current.disabled = false), 500)
			})
			.catch(err => {
				notifyError(err.reason)
			})
	}

	const calculateNextPosition = () => {
		const length = workoutsDetailsList.length
		if (length > 0) {
			const lastOrderPosition = workoutsDetailsList[length - 1]?.order
			return lastOrderPosition ? lastOrderPosition + 100000 : length + 100000
		} else {
			return 100000
		}
	}

	// ━━━━━━━━━━━━━━━━━━ Sortable functions ━━━━━━━━━━━━━━━━━━ //
	const onAdd = ({ newIndex, item }) => {
		const detailsList = _.clone(workoutsDetailsList)
		let detailForUpdate = _.find(data, detail => detail._id === item.id)
		detailsList.splice(newIndex, 0, detailForUpdate)

		if (newIndex === 0) {
			detailForUpdate.order = Math.trunc(detailsList[1].order / 2)
		} else if (newIndex === detailsList.length - 1) {
			detailForUpdate.order = Math.trunc(detailsList[newIndex - 1].order + 100000)
		} else {
			detailForUpdate.order = Math.trunc((detailsList[newIndex - 1].order + detailsList[newIndex + 1].order) / 2)
		}
		const detail = {
			_id: detailForUpdate._id,
			order: detailForUpdate.order || 100000,
		}
		Meteor.callAsync('workoutDetail.onAdd', detail)
			.then(() => setChangeDetailListValue())
			.catch(error => {
				notifyError('Error on save detail. Try again')
				if (Meteor.isDevelopment) {
					console.error(error)
				}
			})
	}

	const onEnd = ({ newIndex, oldIndex, oldDraggableIndex, newDraggableIndex, pullMode }) => {
		const itemDragging = workoutsDetailsList[oldIndex]
		if (pullMode === true) {
			return
		}

		if (newIndex === 0) {
			const itemBelow = workoutsDetailsList[newIndex]
			itemDragging.order = Math.trunc(itemBelow.order / 2)
		} else if (newIndex === workoutsDetailsList.length - 1) {
			const itemAbove = workoutsDetailsList[newIndex]
			itemDragging.order = itemAbove.order + 100000
		} else if (newIndex < oldIndex) {
			const itemAbove = workoutsDetailsList[newIndex - 1]
			const itemBelow = workoutsDetailsList[newIndex]
			itemDragging.order = Math.trunc((itemAbove.order + itemBelow.order) / 2)
		} else if (newIndex > oldIndex) {
			const itemAbove = workoutsDetailsList[newIndex]
			const itemBelow = workoutsDetailsList[newIndex + 1]
			itemDragging.order = Math.trunc((itemAbove.order + itemBelow.order) / 2)
		}

		if (itemDragging.hasOwnProperty('suborder')) {
			_.each(itemDragging?.items, item => {
				const childUpdate = {
					_id: item._id,
					order: itemDragging?.order || 100000,
				}
				upsertDetail(childUpdate)
			})
		}
		const parentUpdate = {
			_id: itemDragging._id,
			order: itemDragging.order || 100000,
		}
		upsertDetail(parentUpdate)
	}

	const upsertDetail = detail => {
		Meteor.callAsync('workoutDetail.upsert', detail)
			.then(() => {
				setChangeDetailListValue()
				setChangeRowDetail()
			})
			.catch(error => {
				notifyError('Error on save detail. Try again')
				if (Meteor.isDevelopment) {
					console.error(error)
				}
			})
	}

	const onMove = evt => {
		const end = _.find(data, detail => detail._id === evt.related.id)
		const move = _.find(data, detail => detail._id === evt.dragged.id)

		return !(move === undefined && end?.suborder)
	}

	const sortableOptions = {
		handle: '.drag-main',
		ghostClass: 'ghost',
		group: 'main',
		fallbackOnBody: true,
		fallbackTolerance: 10,
		animation: 150,
		onEnd,
		onAdd,
		onMove,
	}

	const selectRow = detail => {
		if (!detail) return

		if (detail.hasOwnProperty('superset')) {
			return (
				<SupersetGroup
					key={`superset-${detail._id}`}
					id={detail._id}
					workout={workout}
					superset={detail}
					exercises={exercises}
					coachConfiguration={coachConfiguration}
				/>
			)
		} else {
			return (
				<WorkoutDetailsTableRow
					detailsList={workoutsDetailsList}
					key={`detail-${detail._id}`}
					detailB={detail}
					workout={workout}
					detailId={detail._id}
					exercises={exercises}
					origin={'main'}
					coachConfiguration={coachConfiguration}
					updateSupersetRow={setChangeDetailListValue}
				/>
			)
		}
	}

	return (
		<>
			<TableContainer
				component={Paper}
				style={{
					overflowX: 'auto',
					boxShadow:
						'0px 0px 1px -1px rgb(0 0 0 / 0.20), 0px 1px 1px 0px rgb(0 0 0 / 0.14), 0px 1px 3px 0px rgb(0 0 0 / 0.12)',
					border: '1px solid var(--gray-300)',
					borderRadius: '8px',
					position: 'relative',
					width: '100%',
				}}>
				<WorkoutsSupersetCustomTable
					sx={{
						minWidth: 650,
						minHeight: '30px',
						backgroundColor: '#fff',
					}}
					size="small"
					aria-label="simple table">
					<WorkoutDetailsTableHeader workout={workout} />

					<ReactSortable
						tag={CustomTableBodyComponent}
						list={workoutsDetailsList}
						setList={newState => {
							setWorkoutsDetailsList(newState)
						}}
						{...sortableOptions}>
						{workoutsDetailsList.map(detail => selectRow(detail))}
					</ReactSortable>
					<TableFooter>
						<TableRow>
							<TableCell colSpan={getFieldsEnabled(workout).length + 2}>
								<Button
									onClick={() => addDetail()}
									size="large"
									color="secondary"
									variant="text"
									ref={btnRef}>
									<i className="fas fa-plus" /> Add a new row
								</Button>
							</TableCell>
						</TableRow>
					</TableFooter>
				</WorkoutsSupersetCustomTable>
			</TableContainer>
		</>
	)
}

export default WorkoutDetailsTable
