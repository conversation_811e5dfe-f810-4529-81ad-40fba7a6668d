import React, { useState } from 'react'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faGear, faMicrophone, faPlay, faStop, faTrashCan, faWaveform } from '@fortawesome/pro-regular-svg-icons'
import matchPattern from '../../utility/dev/matchPattern'
import MicRecorder from 'mic-recorder-to-mp3'
import { ButtonContained, ButtonOutlined } from '@ui-common/components/forms'

const css = {
	container: {
		display: 'flex',
		alignItems: 'center',
		justifyContent: 'center',
		gap: '1rem',
		width: '100%',
		height: '45px',
		cursor: 'pointer',
		borderRadius: '12px',
	},
	buttonLabel: {
		fontWeight: 500,
	},
}

const AudioStateEnum = {
	STOP: 'STOP',
	RECORDING: 'RECORDING',
	PLAYING: 'PLAYING',
	PROCESSING: 'PROCESSING',
}

const recorder = new MicRecorder({
	bitRate: 128,
})

const Mp3AudioRecorder = props => {
	const [audio, setAudio] = useState(null)
	const [audioChunks, setAudioChunks] = useState([])
	const [audioState, setAudioState] = useState(AudioStateEnum.STOP)
	const { onRecording, onStopRecording, onPlaying, onStopPlaying, onFinishEncode, onRemoveAudio } = props

	const audioPlayerRef = React.useRef(new Audio())

	const detectSupportedMimeType = () => {
		const AudioMimeTypes = ['audio/webm;codecs=opus', 'audio/mp4;codecs=mp4a', 'audio/webm;codecs=pcm']
		return AudioMimeTypes.find(mimeType => {
			return MediaRecorder.isTypeSupported(mimeType)
		})
	}

	React.useEffect(() => {
		if (!audioChunks.length) return
		const audioBlob = new Blob(audioChunks, {
			type: detectSupportedMimeType(),
		})
		const audioUrl = URL.createObjectURL(audioBlob)

		audioPlayerRef.current.src = audioUrl
		setAudio(audioUrl)
		setAudioState(AudioStateEnum.STOP)
		onFinishEncode?.(audioBlob, audioUrl)
	}, [audioChunks])

	const handleStartRecording = async () => {
		recorder
			.start()
			.then(() => {
				setAudioState(AudioStateEnum.RECORDING)
			})
			.catch(e => {
				console.error(e)
			})

		onRecording?.()
	}

	const handleStopRecording = () => {
		setAudioState(AudioStateEnum.PROCESSING)
		recorder
			.stop()
			.getMp3()
			.then(([buffer, blob]) => {
				const file = new File(buffer, 'Record.mp3', {
					type: blob.type,
					lastModified: Date.now(),
				})

				const audioUrl = URL.createObjectURL(file)
				audioPlayerRef.current.src = URL.createObjectURL(file)
				setAudio(URL.createObjectURL(file))
				setAudioState(AudioStateEnum.STOP)
				onFinishEncode?.(blob, audioUrl)
				onStopRecording?.()
			})
			.catch(e => {
				console.log(e)
			})
	}

	const stopPlaying = () => {
		audioPlayerRef.current.pause()
		audioPlayerRef.current.currentTime = 0
		setAudioState(AudioStateEnum.STOP)
		onStopPlaying?.()
	}

	const startPlaying = async () => {
		await audioPlayerRef.current.play()
		audioPlayerRef.current.onended = () => {
			stopPlaying()
		}
		setAudioState(AudioStateEnum.PLAYING)
		onPlaying?.()
	}

	const dropAudio = () => {
		setAudio(null)
		setAudioChunks([])
		onRemoveAudio?.()
	}

	return React.useMemo(() => {
		return matchPattern({ audioState, audio }, [
			{
				pattern: { audioState: AudioStateEnum.STOP, audio: null },
				callback: () => (
					<ButtonOutlined
						sx={css.container}
						onClick={handleStartRecording}
						gap={1}>
						<FontAwesomeIcon icon={faMicrophone} />
						Record audio note
					</ButtonOutlined>
				),
			},
			{
				pattern: { audioState: AudioStateEnum.STOP, audio: '$exists' },
				callback: () => (
					<>
						<ButtonOutlined
							sx={css.container}
							onClick={startPlaying}>
							<FontAwesomeIcon icon={faPlay} />
							Play audio note
						</ButtonOutlined>
						<ButtonContained
							fullWidth
							color="error"
							sx={{
								gap: '1rem',
								color: 'error.contrastText',
								backgroundColor: 'transparent',
								'&:hover': {
									backgroundColor: 'error.light',
								},
							}}
							onClick={dropAudio}>
							<FontAwesomeIcon icon={faTrashCan} />
							Remove audio note
						</ButtonContained>
					</>
				),
			},
			{
				pattern: { audioState: AudioStateEnum.RECORDING },
				callback: () => (
					<ButtonOutlined
						sx={css.container}
						onClick={handleStopRecording}>
						<FontAwesomeIcon icon={faWaveform} />
						Recording... Click again to finish
					</ButtonOutlined>
				),
			},
			{
				pattern: { audioState: AudioStateEnum.PROCESSING },
				callback: () => (
					<ButtonOutlined sx={css.container}>
						<FontAwesomeIcon icon={faGear} />
						Processing... Please wait
					</ButtonOutlined>
				),
			},
			{
				pattern: { audioState: AudioStateEnum.PLAYING },
				callback: () => (
					<>
						<ButtonOutlined onClick={stopPlaying}>
							<FontAwesomeIcon icon={faStop} />
							Playing... Click again to stop
						</ButtonOutlined>
						<ButtonContained
							color="error"
							sx={{
								gap: '1rem',
								color: 'error.contrastText',
								backgroundColor: 'error.main',
								'&:hover': {
									backgroundColor: 'error.dark',
								},
							}}
							onClick={dropAudio}>
							<FontAwesomeIcon icon={faTrashCan} />
							Remove audio note
						</ButtonContained>
					</>
				),
			},
		])
	}, [audioState, audio])
}

export default Mp3AudioRecorder
