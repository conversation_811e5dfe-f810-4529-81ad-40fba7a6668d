import React, { useContext } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import TouchAppIcon from '@mui/icons-material/TouchApp'
import ExpandableCard from '../../components/layouts/ExpandableCard'
import FlexRow from '../../components/layouts/FlexRow'
import FlexColumn from '../../components/layouts/FlexColumn'
import _ from 'lodash'
import { ActiveUserContext } from '../../context/coach/ActiveUserDataProvider'
import { TypographyClientBrand } from '../../branded-components/Typography'

const PropertiesEnum = {
	Heading: 'part',
	Superset: 'superset',
	Suborder: 'suborder',
}
const WorkoutCard = ({ workout, goWorkoutDetailCustomer }) => {
	const { branding } = useContext(ActiveUserContext)

	function wrapDetails(workoutDetails) {
		const Filters = {
			IsSimpleRow: detail => {
				return (
					!detail.hasOwnProperty(PropertiesEnum.Superset) && !detail.hasOwnProperty(PropertiesEnum.Suborder)
				)
			},
			IsSupersetWrapperRow: detail => {
				return detail.hasOwnProperty(PropertiesEnum.Superset)
			},
			IsSupersetRow: (detail, supersetWrapper) => {
				return detail.order === supersetWrapper.order && !detail.hasOwnProperty(PropertiesEnum.Superset)
			},
		}

		const simpleRows = _.filter(workoutDetails, detail => {
			return Filters.IsSimpleRow(detail)
		})
		const supersetWrapperRows = _.filter(workoutDetails, detail => {
			return Filters.IsSupersetWrapperRow(detail)
		})
		const supersetRows = _.each(supersetWrapperRows, supersetWrapper => {
			supersetWrapper.details = _.sortBy(
				_.filter(workoutDetails, detail => {
					return Filters.IsSupersetRow(detail, supersetWrapper)
				}),
				'suborder',
			)
		})
		return _.sortBy(_.concat(simpleRows, supersetRows), 'order')
	}

	const renderDetails = () => {
		const cloneDetails = _.cloneDeep(workout?.details)
		const details = wrapDetails(cloneDetails)
		return _.map(details, (detail, key) => {
			return (
				<FlexColumn
					key={key}
					sx={
						detail.superset
							? {
									padding: '8px',
									background: branding?.palette?.[100] ?? 'var(--primary-100)',
									borderRadius: '8px',
								}
							: { padding: '0 8px' }
					}>
					{detail?.superset ? (
						<>
							<Typography
								variant={'title2'}
								sx={{ fontSize: '14px' }}>
								{detail?.movement ?? 'Superset'}
							</Typography>
							{_.map(detail?.details, (detailSuperset, i) => {
								return (
									<Typography
										key={i}
										variant={'description'}>
										{detailSuperset?.movement}
									</Typography>
								)
							})}
						</>
					) : (
						<>
							{detail?.part ? (
								<Typography
									variant={'title2'}
									sx={{
										fontSize: '14px',
										textAlign: 'center',
									}}>
									{detail?.part}
								</Typography>
							) : (
								<Typography variant={'description'}>{detail?.movement}</Typography>
							)}
						</>
					)}
				</FlexColumn>
			)
		})
	}

	return (
		<ExpandableCard onClick={goWorkoutDetailCustomer}>
			<FlexColumn spacing={2}>
				<FlexRow
					center={'vertical'}
					fullWidth
					justifyContent={'space-between'}>
					<TypographyClientBrand
						variant={'title1'}
						sx={{ fontSize: '16px' }}>
						{workout?.title || 'Untitled Plan'}
					</TypographyClientBrand>
					<TouchAppIcon
						sx={{
							color: branding?.palette?.[150] ?? 'var(--primary-100)',
						}}
					/>
				</FlexRow>
				<FlexColumn spacing={1}>{renderDetails()}</FlexColumn>
			</FlexColumn>
		</ExpandableCard>
	)
}

const WorkoutsListUpcomingCustomer = ({ pendingPlans }) => {
	const history = useNavigate()
	const routeParams = useParams()

	const { data: plans } = pendingPlans || { data: [] }

	const goWorkoutDetailCustomer = id => {
		history(`/pt/${routeParams.accessLink}/workout/detail/${id}`)
	}

	const css = {
		subtitle: {
			fontSize: '18px',
			fontWeight: 600,
			lineHeight: '28px',
			color: 'var(--gray-900)',
			marginBottom: '2rem',
		},
	}

	return (
		<Box>
			<Box>
				<Typography sx={css.subtitle}>Next Plan</Typography>
				{plans[0] && plans[0]?.details?.length > 0 ? (
					<WorkoutCard
						workout={plans[0]}
						goWorkoutDetailCustomer={() => goWorkoutDetailCustomer(plans[0]?._id)}
					/>
				) : (
					<p className="empty-label">you have no assigned plans</p>
				)}
			</Box>
			<Box>
				<Typography sx={{ ...css.subtitle, marginTop: '2rem' }}>Upcoming Plans</Typography>
				{plans[1] ? (
					<FlexColumn spacing={2}>
						{plans
							.filter((e, index) => index !== 0)
							.map((element, idx) => (
								<WorkoutCard
									key={idx}
									workout={element}
									goWorkoutDetailCustomer={() => goWorkoutDetailCustomer(element._id)}
								/>
							))}
					</FlexColumn>
				) : (
					<p className="empty-label">you have no upcoming plans</p>
				)}
			</Box>
		</Box>
	)
}

export default WorkoutsListUpcomingCustomer
