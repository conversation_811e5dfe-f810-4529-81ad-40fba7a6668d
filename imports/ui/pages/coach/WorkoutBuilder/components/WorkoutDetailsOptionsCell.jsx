import React, { useState } from 'react'
import TableCell from '@mui/material/TableCell'
import { faEllipsisVertical, faHeading, faPenToSquare, faTrashCan } from '@fortawesome/pro-regular-svg-icons'
import { ToolButtonWhite } from './StyledComponents'
import Stack from '@mui/material/Stack'
import { useWorkoutBuilderData } from '../context/WorkoutBuilderDataProvider'
import WorkoutDetailPropertiesModal from '../../../../components/WorkoutConfigPropertiesModal/WorkoutDetailPropertiesModal'
import Popover from '@mui/material/Popover'
import { GrayConfigBtn } from '../../../../utility/buttons/BackButton'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faLink, faRotateLeft } from '@fortawesome/pro-solid-svg-icons'
import { useKeyboardNavigation } from '../context/KeyboardNavigationProvider'
import {
	useRemovePlanDetail,
	useToggleHeading,
	useTransformSuperset,
} from '@pages/coach/plans/PlanBuilder/hooks/data/usePlanDetailsMutations'
import { useParams } from 'react-router-dom'

export default function WorkoutDetailsOptionsCell({ detail }) {
	const { workoutId } = useParams()
	const { updateDetails, updateExercises } = useWorkoutBuilderData()
	const [showPropertiesModal, setShowPropertiesModal] = useState(false)
	const { pauseKeys, resumeKeys } = useKeyboardNavigation()

	const [anchorEl, setAnchorEl] = useState(null)

	const handleOptions = event => setAnchorEl(event.currentTarget)

	const handleClose = () => setAnchorEl(null)

	const open = Boolean(anchorEl)
	const id = open ? `${detail?._id}-popover` : undefined

	const isHeader = 'part' in detail
	const { isSupersetItem } = detail

	const { mutate: removeDetail } = useRemovePlanDetail({ planId: workoutId })
	const { mutate: toggleHeading } = useToggleHeading({ planId: workoutId })
	const { mutate: transformToSuperset } = useTransformSuperset({ planId: workoutId })

	const handleEdit = () => {
		pauseKeys()
		setShowPropertiesModal(true)
	}

	return (
		<TableCell>
			<Stack
				direction="row"
				justifyContent="space-around">
				<ToolButtonWhite
					icon={faEllipsisVertical}
					title="Options"
					onClick={handleOptions}
				/>
				<ToolButtonWhite
					icon={faPenToSquare}
					title="Edit Properties"
					onClick={handleEdit}
				/>
				<ToolButtonWhite
					icon={faTrashCan}
					title="Remove"
					onClick={() => removeDetail(detail._id)}
				/>

				<Popover
					id={id}
					open={open}
					anchorEl={anchorEl}
					onClose={handleClose}
					anchorOrigin={{
						vertical: 'bottom',
						horizontal: 'left',
					}}
					PaperProps={{
						style: {
							border: '1px solid var(--gray-200)',
							boxShadow:
								'0px 12px 16px -4px rgba(16, 24, 40, 0.1), 0px 4px 6px -2px rgba(16, 24, 40, 0.05)',
							borderRadius: '8px',
							padding: '16px',
							width: '200px',
						},
					}}>
					{!isSupersetItem && (
						<GrayConfigBtn
							sx={{ display: 'flex', gap: 1, mb: 1 }}
							onClick={() => transformToSuperset({ _id: detail._id })}>
							<FontAwesomeIcon icon={faLink} />
							Create Superset
						</GrayConfigBtn>
					)}
					<GrayConfigBtn
						onClick={() => toggleHeading({ _id: detail._id })}
						spacing={2}
						sx={{ display: 'flex', gap: 1 }}>
						{isHeader ? (
							<>
								<FontAwesomeIcon icon={faRotateLeft} />
								Revert to Row
							</>
						) : (
							<>
								<FontAwesomeIcon icon={faHeading} />
								Create Heading
							</>
						)}
					</GrayConfigBtn>
				</Popover>
			</Stack>
			{showPropertiesModal && (
				<WorkoutDetailPropertiesModal
					open={showPropertiesModal}
					workoutDetail={detail}
					updateDetail={() => {
						updateDetails()
						updateExercises()
					}}
					onClose={() => {
						resumeKeys()
						setShowPropertiesModal(false)
					}}
					settings={{
						mediaType: 'video',
						enableVideo: true,
						enableImages: true,
						enableNotes: true,
						enableAudio: true,
					}}
				/>
			)}
		</TableCell>
	)
}
