import React, { useState } from 'react'
import TableRow from '@mui/material/TableRow'
import TableCell from '@mui/material/TableCell'
import { useWorkoutBuilderData } from '../../context/WorkoutBuilderDataProvider'
import WorkoutDetailsOptionsCell from '../WorkoutDetailsOptionsCell'
import { KEYBOARD_CONTROL_MODES, useKeyboardNavigation } from '../../context/KeyboardNavigationProvider'
import TextField from '@mui/material/TextField'
import notifyError from '../../../../../utility/toast/notifyError'
import { DragAndDropHandlerCell } from '../cells/DragAndDropHandlerCell'

export default function WorkoutDetailsTableHeaderRow({ detail }) {
	const { fields, updateDetails } = useWorkoutBuilderData()
	const defaultColumnsCount = 1
	const enabledFieldsCount = fields.filter(field => field.enabled).length

	const [currentValue, setCurrentValue] = useState(detail?.part || '')

	const { row, column, mode, setTarget, setPosition } = useKeyboardNavigation()

	const isSelectedRow = row === detail?.position?.row
	const isSorting = mode === KEYBOARD_CONTROL_MODES.SORT

	const saveHeader = () => {
		Meteor.callAsync('workoutDetails.editHeader', { _id: detail._id, part: currentValue })
			.catch(error => {
				notifyError(error.reason)
			})
			.finally(updateDetails)
	}

	return (
		<TableRow
			id={detail._id}
			className={'header'}
			sx={{
				backgroundColor: isSelectedRow && isSorting ? 'var(--gray-100)' : 'transparent',
				'&.ghost': {
					backgroundColor: 'var(--gray-100)',
				},
			}}>
			<DragAndDropHandlerCell detail={detail} />
			<TableCell
				sx={{ padding: '0 !important', backgroundColor: isSelectedRow ? 'rgb(248, 248, 255)' : 'transparent' }}
				align="center"
				colSpan={enabledFieldsCount + defaultColumnsCount}>
				<TextField
					inputRef={element => {
						if (!isSelectedRow) {
							return null
						}
						setTarget(element)
					}}
					sx={{
						width: '100%',
						height: 'auto',
						border: '1px solid',
						borderColor: isSelectedRow ? 'primary.400' : 'transparent',
						'&:focus-within': {
							border: '1px solid',
							borderColor: 'primary.800',
							outline: '2px solid',
							outlineColor: 'primary.500',
						},
						'& fieldset': {
							border: 'none !important',
						},
						'& .MuiInputBase-input': {
							fontWeight: 'bold !important',
						},
						'& input': {
							textAlign: 'center!important',
						},
					}}
					onBlur={saveHeader}
					onFocus={() => {
						setPosition(detail.position.row, column)
					}}
					value={currentValue}
					onChange={event => {
						setCurrentValue(event.target.value)
					}}
				/>
			</TableCell>
			<WorkoutDetailsOptionsCell detail={detail} />
		</TableRow>
	)
}
