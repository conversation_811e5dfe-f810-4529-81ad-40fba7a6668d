import React, { useContext, useEffect } from 'react'
import Box from '@mui/material/Box'
import { Helmet } from 'react-helmet-async'
import { NavBreadcrumbs } from '../../../../utility/CustomBreadcrumbs/CustomBreadcrumbs'
import Link from '@mui/material/Link'
import Typography from '@mui/material/Typography'
import FlexRow from '../../../../components/layouts/FlexRow'
import DocumentationLink from '../../../../components/doc/DocumentationLink'
import DocumentationLinks from '../../../../constants/DocumentationLinksMap'
import UpdateLoading from '../../../../components/UpdateLoading/UpdateLoading'
import { Title } from '../../../../utility/labels/CustomLabels'
import { getClientNameById } from '../services/clientServices'
import FlexColumn from '../../../../components/layouts/FlexColumn'
import TextInput from '../../../../components/forms/TextInput'
import Tooltip from '@mui/material/Tooltip'
import { exportWorkoutToPDF } from '../../../../utility/WorkoutUtils/WorkoutUtils'
import notifySuccess from '../../../../utility/toast/notifySuccess'
import notifyError from '../../../../utility/toast/notifyError'
import { faCopy, faFloppyDisk, faKeyboardLeft, faMobile, faPrint, faUserTag } from '@fortawesome/pro-regular-svg-icons'
import ButtonFontAwesomeIcon from '../../../../components/forms/ButtonFontAwesomeIcon'
import ConfigWorkoutProperties from '../../../../components/ConfigWorkoutProperties/ConfigWorkoutProperties'
import { ConfigPropertiesBtn } from '../../../../utility/buttons/BackButton'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import BrandedButton from '../../../../components/atoms/BrandedButton'
import { Show } from '../../../../components/utils/Show'
import { useWorkoutBuilderData } from '../context/WorkoutBuilderDataProvider'
import useToggle from '../../../../hooks/useToggle'
import { CommandContext } from '../../../../context/commands/CommandProvider'
import ViewPlanAsClientModal from '../../../../components/Modals/ViewPlanAsClientModal'
import { useKeyboardNavigation } from '../context/KeyboardNavigationProvider'
import { useNavigate } from 'react-router-dom'
import { Meteor } from 'meteor/meteor'
import { setChangeWorkoutValue } from '@collections/states/workoutPageState'
import Stack from '@mui/material/Stack'

export default function WorkoutBuilderForm() {
	const { workout, updateDetails, readOnly, useKeyboard } = useWorkoutBuilderData()
	const { toggleKeyboardControl, enabled, pauseKeys, resumeKeys } = useKeyboardNavigation()

	const { _id: workoutId, clientId, templateId } = workout || {}
	const isCompleted = workout?.status === 'done'

	const [clientName, setClientName] = React.useState(null)
	const [isExportPending, setExportPending] = React.useState(false)
	const [title, setTitle] = React.useState(workout?.title || '')

	const [showViewAsClientModal, toggleShowViewAsClientModal] = useToggle(false)
	const { setCommand } = useContext(CommandContext)
	const history = useNavigate()

	const handleOpenViewAsClientModal = () => {
		updateDetails()
		toggleShowViewAsClientModal()
	}

	const getBreadcrumbs = route => {
		const crumbs = {
			// {route: Array([label, path])}
			draft: [['Drafts', '/app/templates?option=drafts']],
			client: [
				['Clients', '/app/clients'],
				['Edit Client', `/app/client/edit/${clientId}`],
			],
			template: [
				['Program Templates', '/app/templates'],
				['Template Detail', `/app/templates/${templateId}`],
			],
		}
		return crumbs[route]
	}
	const Breadcrumbs = () => {
		let crumbs = getBreadcrumbs('draft')
		if (clientId) crumbs = getBreadcrumbs('client')
		if (templateId) crumbs = getBreadcrumbs('template')
		return (
			<NavBreadcrumbs
				separator="›"
				aria-label="breadcrumb">
				{crumbs.map(([title, href], index) => (
					<Link
						key={index}
						color="inherit"
						href={href}
						underline="hover">
						{title}
					</Link>
				))}
				<Typography>Plan</Typography>
			</NavBreadcrumbs>
		)
	}

	const exportWorkout = async () => {
		if (isExportPending) return notifyError('Please wait for the current export to finish')
		setExportPending(true)
		const exportResult = await exportWorkoutToPDF({ workoutId, title })
		if (exportResult) {
			setExportPending(false)
			notifySuccess('Plan was exported')
		} else {
			setExportPending(false)
			notifyError('Error to export plan')
		}
	}

	useEffect(() => {
		clientId && getClientNameById(clientId).then(name => setClientName(name))
	}, [clientId])

	const saveTitle = () => {
		Meteor.callAsync('workout.updateTitle', { _id: workoutId, title }).catch(error => {
			notifyError('Update Title error')
			console.error(error)
		})
	}

	const onDoneWorkout = () => {
		notifySuccess('Plan saved')
		history(-1)
	}

	const onPublishWorkout = action => {
		if ((clientId || workout?.templateId) && action === 'publish') {
			if (clientId) {
				const payload = {
					clientId,
					workoutId: workout._id,
				}

				Meteor.callAsync('workout.publishWorkoutToClient', payload)
					.then(() => {
						setChangeWorkoutValue(27)
					})
					.catch(error => {
						console.error('Error to change status')
						console.error(error)
					})
			} else if (workout?.templateId) {
				const payload = { workoutId: workoutId, templateId: workout?.templateId }
				Meteor.callAsync('workout.publishWorkout', payload)
					.then(() => {
						setChangeWorkoutValue(28)
					})
					.catch(error => {
						console.error('Error to change status')
						console.error(error)
					})
			}
			history(-1)
		} else {
			setCommand({ type: 'publishPlan', args: { planId: workoutId } })
		}
	}

	const onSaveDraft = () => {
		const payload = {
			_id: workoutId,
			path: 'Workoutpage1',
			modifier: {
				$set: {
					creationStatus: 'DRAFT',
				},
			},
		}
		Meteor.callAsync('workout.update', payload)
			.then(response => {
				notifySuccess('Plan saved')
				history(-1)
			})
			.catch(error => {
				console.log(error)
			})
	}

	return (
		<>
			<Helmet>
				<title lang="en">Plan</title>
			</Helmet>
			<Box component={'section'}>
				<FlexRow
					justifyContent={'space-between'}
					alignItems={'center'}
					fullWidth>
					<Breadcrumbs />
					<DocumentationLink docs={DocumentationLinks.PlanBuilder} />
				</FlexRow>
				<Title
					sx={{ mb: 4 }}
					component="h1">
					{isCompleted ? 'Completed Plan' : 'New Plan'}
					<UpdateLoading />
				</Title>
				<Stack
					direction={{ xs: 'column', lg: 'row' }}
					alignItems={{ xs: 'center', lg: 'flex-end' }}>
					<FlexRow
						gap={2}
						sx={{ alignSelf: 'flex-end', display: { xs: 'flex', md: 'none' } }}>
						<Tooltip
							placement="top"
							title="Enable keyboard movements">
							<ButtonFontAwesomeIcon
								variant={useKeyboard ? 'pushed' : 'shadow'}
								shape={'rounded'}
								icon={faKeyboardLeft}
								onClick={toggleKeyboardControl}
								color={enabled ? 'var(--primary-700)' : 'var(--gray-200)'}
							/>
						</Tooltip>
						<Tooltip
							placement="top"
							title="Print plan">
							<ButtonFontAwesomeIcon
								variant={'shadow'}
								shape={'rounded'}
								icon={faPrint}
								onClick={exportWorkout}
								color={isExportPending ? 'var(--gray-200)' : 'var(--gray-500)'}
							/>
						</Tooltip>
					</FlexRow>
					<FlexColumn sx={{ width: { xs: '95%', lg: '25%' } }}>
						{clientName && (
							<Typography sx={{ mb: 2, fontWeight: 'bold' }}>Plan for: {clientName}</Typography>
						)}
						<TextInput
							fullWidth
							label={
								<p>
									Plan name:<span style={{ color: 'red' }}>*</span>
								</p>
							}
							value={title}
							onChange={e => {
								if (readOnly) return notifyError('Completed plans cannot be edited.')
								setTitle(e.target.value)
							}}
							onFocus={pauseKeys}
							onBlur={() => {
								resumeKeys()
								saveTitle()
							}}
						/>
					</FlexColumn>
					<Stack
						direction="column"
						sx={{ width: { xs: '100%', lg: '75%' } }}
						gap={1}
						p={{ xs: 2, lg: 0 }}>
						<FlexRow
							gap={2}
							sx={{ justifyContent: 'flex-end', display: { xs: 'none', md: 'flex' } }}>
							<Tooltip
								placement="top"
								title="Enable keyboard movements">
								<ButtonFontAwesomeIcon
									variant={useKeyboard ? 'pushed' : 'shadow'}
									shape={'rounded'}
									icon={faKeyboardLeft}
									onClick={toggleKeyboardControl}
									color={enabled ? 'var(--primary-700)' : 'var(--gray-200)'}
								/>
							</Tooltip>
							<Tooltip
								placement="top"
								title="Print plan">
								<ButtonFontAwesomeIcon
									variant={'shadow'}
									shape={'rounded'}
									icon={faPrint}
									onClick={exportWorkout}
									color={isExportPending ? 'var(--gray-200)' : 'var(--gray-500)'}
								/>
							</Tooltip>
						</FlexRow>

						<Stack
							direction="row"
							flexWrap={'wrap'}
							sx={{ gap: '12px' }}
							justifyContent="flex-end">
							<ConfigWorkoutProperties />
							<ConfigPropertiesBtn
								sx={{ width: { lg: '180px', sm: '49%', xs: '100%' } }}
								onClick={handleOpenViewAsClientModal}>
								<FontAwesomeIcon
									icon={faMobile}
									style={{ marginRight: '8px' }}
								/>
								View as Client
							</ConfigPropertiesBtn>
							<Show>
								<Show.Case condition={workout?.creationStatus === 'DRAFT'}>
									<BrandedButton
										disabled={!workout?.title}
										sx={{
											marginLeft: '12px!important',
											width: { lg: '180px', sm: '49%', xs: '100%' },
										}}
										onClick={onSaveDraft}>
										<FontAwesomeIcon
											icon={faFloppyDisk}
											style={{ marginRight: '8px' }}
										/>
										Save as Draft
									</BrandedButton>
								</Show.Case>
								<Show.Case condition={workout?.creationStatus === 'READY'}>
									<BrandedButton
										disabled={!workout?.title}
										sx={{ width: { lg: '180px', sm: '49%', xs: '100%' } }}
										onClick={() => setCommand({ type: 'copyTo', args: { planId: workoutId } })}>
										<FontAwesomeIcon
											icon={faCopy}
											style={{ marginRight: '8px' }}
										/>
										Copy To
									</BrandedButton>
								</Show.Case>
							</Show>

							<Show>
								<Show.Case condition={workout?.creationStatus === 'DRAFT'}>
									<BrandedButton
										disabled={!workout?.title}
										sx={{
											marginLeft: '12px!important',
											width: { lg: '180px', sm: '49%', xs: '100%' },
										}}
										onClick={() => {
											onPublishWorkout('publish')
										}}>
										<FontAwesomeIcon
											icon={faUserTag}
											style={{ marginRight: '8px' }}
										/>
										Publish plan
									</BrandedButton>
								</Show.Case>
								<Show.Default>
									<BrandedButton
										disabled={!workout?.title}
										sx={{ width: { lg: '180px', sm: '49%', xs: '100%' } }}
										onClick={onDoneWorkout}>
										<FontAwesomeIcon
											icon={faFloppyDisk}
											style={{ marginRight: '8px' }}
										/>
										Save
									</BrandedButton>
								</Show.Default>
							</Show>
						</Stack>
					</Stack>
				</Stack>
			</Box>
			{showViewAsClientModal && (
				<ViewPlanAsClientModal
					open={showViewAsClientModal}
					onClose={toggleShowViewAsClientModal}
					workoutId={workoutId}
					clientId={clientId}
				/>
			)}
		</>
	)
}
