import React, { useState } from 'react'
import Paper from '@mui/material/Paper'
import Box from '@mui/material/Box'
import IconButton from '@mui/material/IconButton'
import MenuItem from '@mui/material/MenuItem'
import Select from '@mui/material/Select'
import TableCell from '@mui/material/TableCell'
import TableContainer from '@mui/material/TableContainer'
import TableFooter from '@mui/material/TableFooter'
import Typography from '@mui/material/Typography'
import TableHead from '@mui/material/TableHead'
import TableRow from '@mui/material/TableRow'
import TableBody from '@mui/material/TableBody'
import Tooltip from '@mui/material/Tooltip'
import { FontAwesomeIcon } from '@fortawesome/react-fontawesome'
import { faPenToSquare, faThumbtack, faTrashCan } from '@fortawesome/pro-regular-svg-icons'
import { faThumbtack as faThumbtackSolid } from '@fortawesome/pro-solid-svg-icons'
import { CustomNotesTable } from '../../../../utility/tables/CustomTables'
import moment from 'moment/moment'
import Pagination from '@mui/material/Pagination'
import { Meteor } from 'meteor/meteor'
import notifyError from '../../../../utility/toast/notifyError'
import notifySuccess from '../../../../utility/toast/notifySuccess'
import EditClientNotesModal from '../../../../components/Modals/EditClientNotesModal'
import useStaticGrapher2 from '../../../../hooks/useStaticGrapher2'
import { getCoachClientsNotes } from '../../../../../api/coachClientsNotes/coachClientsNotes_Q'
import { useLoadingToast } from '../../../../hooks/useLoadingToast'
import usePersistentState from '../../../../hooks/usePersistentState'
import { map } from 'lodash'
import RichTextEditor from '../../../../components/forms/RichTextEditor'

const ClientNotesList = ({ clientId, insertChange }) => {
	const [page, setPage] = usePersistentState('ClientsNotesPage')
	const [paginationLimit, setPaginationLimit] = usePersistentState('ClientsNotesLimit')
	const [openEditModal, setOpenEditModal] = useState(false)
	const [noteToEdit, setNoteToEdit] = useState(null)
	const [change, setChange] = useState(0)
	const [LoadingToastComponent, toastControls] = useLoadingToast()
	const onChange = () => setChange(change + 1)

	const { data, ready, count } = useStaticGrapher2({
		getQuery: getCoachClientsNotes,
		singleton: false,
		refresh: false,
		settings: {
			filters: { clientId },
			options: {
				limit: paginationLimit,
				skip: (page - 1) * paginationLimit,
				sort: { pinned: -1, createdAt: -1 },
			},
		},
		debug: false,
		inputs: [page, paginationLimit, insertChange, change],
	})

	const handlePagination = (event, value) => {
		setPage(value)
	}

	const handleLimitChange = event => {
		setPaginationLimit(event.target.value)
	}

	const handlePinToTop = note => {
		Meteor.callAsync('coachClientsNotes.pin', { _id: note._id, clientId })
			.then(() => {
				onChange()
			})
			.catch(error => {
				notifyError(error.reason)
			})
	}

	const handleDelete = note => {
		Meteor.callAsync('coachClientsNotes.remove', { _id: note._id })
			.then(() => {
				notifySuccess('Note deleted successfully')
				onChange()
			})
			.catch(error => {
				notifyError(error.reason)
			})
	}

	const handleEdit = note => {
		setNoteToEdit(note)
		setOpenEditModal(true)
	}

	return (
		<>
			<TableContainer
				component={Paper}
				style={{
					overflowX: 'auto',
					border: '1px solid var(--gray-300)',
					boxShadow: 'none',
					borderRadius: '8px',
					width: '100%',
					marginBottom: '35px',
				}}>
				<CustomNotesTable sx={{ minWidth: 650 }}>
					<TableHead>
						<TableRow sx={{ padding: '0px' }}>
							<TableCell
								align="left"
								sx={{ width: '180px', textAlign: 'center' }}>
								Date
							</TableCell>
							<TableCell align="left">Content</TableCell>
							<TableCell
								align="left"
								style={{ width: '140px', textAlign: 'center' }}>
								Actions
							</TableCell>
						</TableRow>
					</TableHead>
					<TableBody>
						{count > 0 ? (
							map(data, (note, index) => {
								return (
									<TableRow
										key={index}
										sx={{
											border: '1px solid var(--gray-600)',
											background: note?.pinned ? 'var(--primary-25)' : 'white',
											padding: '0px',
										}}>
										<TableCell
											align="left"
											sx={{
												width: '180px',
												textAlign: 'center',
											}}>
											{moment(note?.createdAt).format('MMMM Do YYYY')}
										</TableCell>
										<TableCell align="left">
											<RichTextEditor
												value={note?.content}
												readOnly={true}
											/>
										</TableCell>
										<TableCell
											align="left"
											style={{
												width: '140px',
												textAlign: 'center',
											}}>
											<Tooltip title="Edit">
												<IconButton
													onClick={() => handleEdit(note)}
													aria-label="Edit">
													<FontAwesomeIcon
														icon={faPenToSquare}
														size={'xs'}
													/>
												</IconButton>
											</Tooltip>
											<Tooltip title="Pin To Top">
												<IconButton
													onClick={() => handlePinToTop(note)}
													aria-label="Pin to top">
													{note?.pinned ? (
														<FontAwesomeIcon
															icon={faThumbtackSolid}
															size={'xs'}
															color={'var(--primary-600)'}
														/>
													) : (
														<FontAwesomeIcon
															icon={faThumbtack}
															size={'xs'}
														/>
													)}
												</IconButton>
											</Tooltip>
											<Tooltip title="Delete">
												<IconButton
													onClick={() => handleDelete(note)}
													aria-label="Delete">
													<FontAwesomeIcon
														icon={faTrashCan}
														size={'xs'}
													/>
												</IconButton>
											</Tooltip>
										</TableCell>
									</TableRow>
								)
							})
						) : (
							<TableRow
								sx={{
									border: '1px solid var(--gray-300)',
									padding: '0px',
								}}>
								<TableCell
									colSpan={3}
									align="center">
									Looks like there are no notes for this client
								</TableCell>
							</TableRow>
						)}
					</TableBody>
					<TableFooter>
						<TableRow style={{ borderTop: '1px solid #e4e7eb' }}>
							<TableCell
								scope="row"
								colSpan={6}
								align="center">
								{count > 0 && (
									<Box
										sx={{
											display: 'flex',
											flexDirection: 'row',
											alignItems: 'center',
											justifyContent: 'space-between',
											gap: '10px',
											padding: '0 30px',
										}}>
										<Box
											sx={{
												display: 'flex',
												flexDirection: 'row',
												gap: '15px',
												alignItems: 'center',
											}}>
											{count > 5 && (
												<>
													<Typography variant={'body'}>Rows per page:</Typography>
													<Select
														value={paginationLimit}
														onChange={handleLimitChange}
														sx={{
															width: '80px',
															height: '40px',
															fontSize: '14px',
															color: 'var(--gray-500)',
															borderRadius: '10px',
															padding: '0',
															'& .Mui-focused': {
																border: 'none',
															},
														}}>
														<MenuItem value={5}>5</MenuItem>
														{count > 5 && <MenuItem value={10}>10</MenuItem>}
														{count > 10 && <MenuItem value={20}>20</MenuItem>}
														{count > 20 && <MenuItem value={40}>40</MenuItem>}
													</Select>
												</>
											)}
										</Box>
										<Box
											sx={{
												display: 'flex',
												gap: '20px',
												alignItems: 'center',
											}}>
											<Typography variant={'body'}>
												{paginationLimit * (page - 1) + 1} -{' '}
												{paginationLimit * page > count
													? count
													: paginationLimit * (page - 1) + paginationLimit}{' '}
												of {count}
											</Typography>
											<Pagination
												count={Math.ceil(count / paginationLimit)}
												shape="rounded"
												page={page}
												onChange={handlePagination}
											/>
										</Box>
									</Box>
								)}
							</TableCell>
						</TableRow>
					</TableFooter>
				</CustomNotesTable>
			</TableContainer>
			{openEditModal && (
				<EditClientNotesModal
					open={openEditModal}
					handleClose={() => {
						onChange()
						setOpenEditModal(false)
					}}
					note={noteToEdit}
				/>
			)}
			{LoadingToastComponent}
		</>
	)
}

export default ClientNotesList
