import React from 'react'
import But<PERSON> from '@mui/material/Button'
import Dialog from '@mui/material/Dialog'
import DialogActions from '@mui/material/DialogActions'
import DialogContent from '@mui/material/DialogContent'
import DialogTitle from '@mui/material/DialogTitle'
import notifyError from '../../../../../utility/toast/notifyError'
import notifySuccess from '../../../../../utility/toast/notifySuccess'
import { CoachClients } from '../../../../../../api/coachClients/coachClients'
import RotateRightIcon from '@mui/icons-material/RotateRight'

if (Meteor.isClient) {
	import('react-phone-number-input/style.css')
}

export default function SendLinkConfirmationModal(props) {
	const [open, setOpen] = React.useState(false)
	const [sending, setSending] = React.useState(false)

	const handleClickOpen = () => {
		setOpen(true)
	}

	const handleClose = () => {
		props.closeModal()
	}
	const sendLink = () => {
		setSending(true)
		const urlClient = getUrl()
		update(urlClient)
		sendEmail(urlClient)
	}

	const sendEmail = clientUrl => {
		const firstName = props?.client?.profile?.firstName || 'Hi'
		const coachName = Meteor.user().profile?.firstName
		const to = props?.client?.profile?.email
		Meteor.callAsync('sendEmailLink', firstName, coachName, clientUrl, to)
			.then(response => {
				onSuccess('Mail sent')
				handleClose()
			})
			.catch(error => {
				onError('Error to send mail')
			})
			.finally(() => {
				setSending(false)
			})
	}

	const getUrl = () => {
		const usernameCoach = props?.coach?.username ? props?.coach?.username : 'coachUsername'
		const namesClientAsArray = props?.client?.profile?.firstName.split(' ')
		let url = `pt/${usernameCoach}/${namesClientAsArray[0]}`
		const finalUrl = verifyUser(url)
		return `${finalUrl}`
	}

	const verifyUser = async url => {
		let userFound = await CoachClients.find({
			accessLink: url,
		}).fetchAsync()
		let urlFinal = url
		let cont = 1
		while (userFound.length > 0) {
			// esto esta mal
			urlFinal = url.replace(`-${cont}`, '')
			urlFinal = url + `-${cont}`
			userFound = await CoachClients.find({
				accessLink: urlFinal,
			}).fetchAsync()
			cont++
		}
		return urlFinal
	}

	const update = async clientUrl => {
		const coachClient = await CoachClients.find(props.coachClientId).fetchAsync()
		if (coachClient && coachClient.length > 0) {
			const coachClientData = coachClient[0]
			coachClientData.linkSent = true
			coachClientData.accessLink = clientUrl
			Meteor.callAsync('coachClients.upsert', coachClientData)
				.then(response => {
					onSuccess('Link sent')
					handleClose()
				})
				.catch(error => {
					onError('Error on update URL')
				})
		}
	}

	const onError = message => {
		notifyError(message, {
			position: 'top-center',
			autoClose: true,
			hideProgressBar: false,
			closeOnClick: true,
			pauseOnHover: false,
			draggable: true,
			progress: undefined,
		})
	}

	const onSuccess = message => {
		notifySuccess(message, {
			position: 'top-center',
			autoClose: true,
			hideProgressBar: false,
			closeOnClick: true,
			pauseOnHover: false,
			draggable: true,
			progress: undefined,
		})
	}

	return (
		<div>
			<Dialog
				open={props.open || open}
				onClose={handleClose}>
				<DialogTitle>Send link</DialogTitle>
				<DialogContent>
					<div className={'send-link-container'}>
						{!sending && 'Send Link to ' + props?.clientName + '?'}
						{sending ? (
							<span>
								<RotateRightIcon> </RotateRightIcon>
								{'In progress..'}
							</span>
						) : null}
					</div>
				</DialogContent>
				<DialogActions>
					<Button
						size="small"
						color="primary"
						variant="outlined"
						onClick={handleClose}>
						Cancel
					</Button>
					<Button
						size="small"
						color="primary"
						variant="outlined"
						onClick={sendLink}>
						Send
					</Button>
				</DialogActions>
			</Dialog>
		</div>
	)
}
