import React from 'react'
import TableCell from '@mui/material/TableCell'
import moment from 'moment/moment'
import { useGrapherQuery } from '@ui-libs/create-query/useGrapherQuery'
import { getLastComment } from '@collections/workoutsComments/workoutsUserCoachComments_Q'
import { Minutes } from '/imports/shared/common/helpers/time.helpers'

function LastCommentRow({ clientId }) {
	const { data: lastComment } = useGrapherQuery({
		query: getLastComment,
		single: true,
		params: {
			filters: { clientId },
			options: {
				sort: { createdAt: -1 },
			},
		},
		staleTime: Minutes(10),
		refetchOnMount: false,
		refetchOnWindowFocus: false,
		refetchOnReconnect: false,
	})

	return (
		<TableCell align="left">
			{lastComment ? `${moment(lastComment.createdAt).fromNow()}` : 'No Plans Completed'}
		</TableCell>
	)
}

export default React.memo(LastCommentRow, (prevProps, nextProps) => {
	return prevProps.clientId === nextProps.clientId
})
