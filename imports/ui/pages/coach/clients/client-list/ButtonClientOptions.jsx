import React from 'react'
import MenuItem from '@mui/material/MenuItem'
import Button from '@mui/material/Button'

// Tables
import { ButtonGroup } from '@mui/material'
import ArrowDropDownIcon from '@mui/icons-material/ArrowDropDown'
import Popper from '@mui/material/Popper'
import Grow from '@mui/material/Grow'
import Paper from '@mui/material/Paper'
import ClickAwayListener from '@mui/material/ClickAwayListener'
import MenuList from '@mui/material/MenuList'
import { Meteor } from 'meteor/meteor'
import { useNavigate } from 'react-router-dom'

if (Meteor.isClient) {
	import('./client-list-button-actions.css')
}

export default ({ row }) => {
	const history = useNavigate()
	const options = ['Edit', 'Add workout']

	const [open, setOpen] = React.useState(false)
	const anchorRef = React.useRef(null)
	const [selectedIndex, setSelectedIndex] = React.useState(0)

	const handleClick = () => {}

	const handleMenuItemClick = (event, index) => {
		setSelectedIndex(index)
		setOpen(false)
	}

	const handleToggle = () => {
		setOpen(prevOpen => !prevOpen)
	}

	const handleClose = event => {
		if (anchorRef.current && anchorRef.current.contains(event.target)) {
			return
		}

		setOpen(false)
	}

	return (
		<>
			<div
				className="button-group-container"
				style={{ width: 190 }}>
				<ButtonGroup
					fullWidth={true}
					variant="contained"
					ref={anchorRef}
					aria-label="split button">
					<Button
						fullWidth={true}
						id={row._id}
						onClick={handleClick}>
						{options[selectedIndex]}
					</Button>
					<Button
						size="small"
						aria-controls={open ? row._id : undefined}
						aria-expanded={open ? 'true' : undefined}
						aria-label="select merge strategy"
						aria-haspopup="menu"
						style={{ width: 40 }}
						onClick={handleToggle}>
						<ArrowDropDownIcon />
					</Button>
				</ButtonGroup>
				<Popper
					open={open}
					anchorEl={anchorRef.current}
					role={undefined}
					transition
					disablePortal
					strategy={'absolute'}
					className={'popper-class-v2'}>
					{({ TransitionProps, placement }) => (
						<Grow
							{...TransitionProps}
							style={{
								transformOrigin: placement === 'bottom' ? 'top' : 'top',
							}}>
							<Paper>
								<ClickAwayListener onClickAway={handleClose}>
									<MenuList id={row._id}>
										{options.map((option, index) => (
											<MenuItem
												key={option}
												selected={index === selectedIndex}
												onClick={event => handleMenuItemClick(event, index)}>
												{option}
											</MenuItem>
										))}
									</MenuList>
								</ClickAwayListener>
							</Paper>
						</Grow>
					)}
				</Popper>
			</div>
		</>
	)
}
