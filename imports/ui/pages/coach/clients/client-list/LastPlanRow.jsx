import React from 'react'
import TableCell from '@mui/material/TableCell'
import moment from 'moment/moment'
import { useGrapherQuery } from '@ui-libs/create-query/useGrapherQuery'
import { getLastWorkout } from '@collections/workouts/workouts_Q'
import { Minutes } from '/imports/shared/common/helpers/time.helpers'

export function LastPlanRow({ clientId }) {
	const { data: lastPlanDone } = useGrapherQuery({
		query: getLastWorkout,
		single: true,
		params: {
			filters: { clientId, status: 'done' },
		},
		staleTime: Minutes(10),
		refetchOnMount: false,
		refetchOnWindowFocus: false,
		refetchOnReconnect: false,
	})

	return (
		<TableCell align="left">
			{lastPlanDone ? `${moment(lastPlanDone.statusCompleteDate).fromNow()}` : 'No Plans Completed'}
		</TableCell>
	)
}
