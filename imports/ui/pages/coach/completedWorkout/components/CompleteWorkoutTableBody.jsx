import React from 'react'
import { Show } from '../../../../components/utils/Show'
import { useWorkoutBuilderData } from '../../WorkoutBuilder/context/WorkoutBuilderDataProvider'
import CompleteWorkoutTableMovementRow from './CompleteWorkoutTableMovementRow'
import TableBody from '@mui/material/TableBody'
import CompleteWorkoutTableSupersetRow from './CompleteWorkoutTableSupersetRow'

export default function CompleteWorkoutTableBody() {
	const { details } = useWorkoutBuilderData()

	return (
		<TableBody>
			<Show>
				<Show.Case condition={details?.length > 0}>
					{details.map(detail => (
						<Show key={detail._id}>
							<Show.Case condition={Boolean(detail?.part)}></Show.Case>
							<Show.Case condition={Boolean(detail?.superset)}>
								<CompleteWorkoutTableSupersetRow detail={detail} />
							</Show.Case>
							<Show.Default>
								<CompleteWorkoutTableMovementRow detail={detail} />
							</Show.Default>
						</Show>
					))}
				</Show.Case>
			</Show>
		</TableBody>
	)
}
