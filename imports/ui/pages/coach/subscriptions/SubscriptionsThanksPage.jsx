import React from 'react'
import Box from '@mui/material/Box'
import Typography from '@mui/material/Typography'
import { useNavigate } from 'react-router-dom'
import { ContainedPrimaryBtn } from '../../../utility/buttons/BackButton'
import Container from '@mui/material/Container'
import Stack from '@mui/material/Stack'

function SubscriptionsThanksPage() {
	const history = useNavigate()
	const user = Meteor.user()
	const { profile } = user

	const goToAccount = () => history('/app/account')

	return (
		<Container
			maxWidth="xl"
			sx={{
				display: 'flex',
				flexDirection: 'column',
				alignItems: 'center',
				justifyContent: 'center',
				height: '90vh',
			}}>
			<Stack
				direction="row"
				sx={{
					p: 18,
					mt: 4,
					alignItems: 'center',
					justifyContent: 'center',
					background: 'white',
					borderRadius: '24px',
					border: '1px solid rgba(0, 0, 0, 0.15)',
				}}>
				<Box
					sx={{
						display: 'flex',
						flexDirection: 'column',
					}}>
					<Typography
						sx={{
							fontWeight: 'bold',
							fontSize: '38px',
						}}
						variant="h2">
						Great decision, {profile?.firstName}
					</Typography>
					<Typography
						sx={{
							fontWeight: 'bold',
							fontSize: '52px',
						}}
						variant="h1">
						Welcome to Pro!
					</Typography>
					<Typography
						sx={{
							fontWeight: 'bold',
							fontSize: '24px',
							mt: 1,
						}}
						variant="h3">
						We knew you were a cool cat
					</Typography>
					<ContainedPrimaryBtn
						variant="contained"
						sx={{
							marginLeft: '24px!important',
							fontSize: '24px!important',
							fontWeight: 'bold!important',
							p: '32px 48px!important',
							maxWidth: '420px',
							mt: '32px',
						}}
						onClick={() => goToAccount()}>
						Go To My Account
					</ContainedPrimaryBtn>
				</Box>
				<Box
					sx={{
						textAlign: 'center',
						width: {
							xs: '100%',
							sm: '100%',
							md: '100%',
							lg: '50%',
						},
					}}>
					<Box
						sx={{
							width: '620px',
							margin: '0 auto',
						}}>
						<img
							src="/assets/images/QC-mascot.png"
							alt="mascot"
						/>
					</Box>
				</Box>
			</Stack>
		</Container>
	)
}

export default SubscriptionsThanksPage
