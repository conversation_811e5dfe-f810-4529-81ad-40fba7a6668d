function formatNumber(number, totalDigits) {
	let numberStr = String(number)
	const zerosToAdd = totalDigits - numberStr.length
	if (zerosToAdd > 0) {
		numberStr = '0'.repeat(zerosToAdd) + numberStr
	}
	return numberStr
}
function usernameFactory(username, index) {
	return {
		username,
		index,
		hash: `#${formatNumber(index, 4)}`,
		full: `${username}#${formatNumber(index, 4)}`,
	}
}
const usernames = [usernameFactory('root', 1), usernameFactory('admin', 1)]

function generateUsername(username) {
	let composedUsername = usernameFactory(username, 1)
	const duplicatedFound = usernames.find(user => user.username === composedUsername.username)
	if (duplicatedFound) {
		const foundIndex = usernames.find(user => user.username === username && user.index === composedUsername.index)
		while (foundIndex) {
			composedUsername = usernameFactory(username, composedUsername.index + 1)
		}
		usernames.push(composedUsername)
	} else {
		usernames.push(composedUsername)
	}
}
