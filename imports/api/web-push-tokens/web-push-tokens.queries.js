import { WebPushTokens } from './web-push-tokens'

export const getTokens = WebPushTokens.createQuery('webPush.getTokens', {
	$filter({ filters, options, params }) {
		const preset = {
			filter: {},
			options: {},
		}
		if (params.filters) {
			Object.assign(filters, params.filters, preset.filter)
		}
		if (params.options) {
			Object.assign(options, params.options, preset.options)
		}
		return { filters, options, params }
	},
	$paginate: true,
	_id: 1,
	userId: 1,
	token: 1,
	enabled: 1,
	deviceToken: 1,
	createdAt: 1,
	lastAccess: 1,
	expireAt: 1,
})
