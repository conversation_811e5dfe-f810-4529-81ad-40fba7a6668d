import { Coupons } from './coupons'

// Replace coupons
// Replace Coupons

export const getCoupons = Coupons.createQuery(
	'getCoupons',
	{
		$filter({ filters, options, params }) {
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},
		$paginate: true,
		user: { profile: 1 },
		dateCreatedAt: 1,
		dateModifiedAt: 1,
	},
	{},
)
