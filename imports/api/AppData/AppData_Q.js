import { AppData } from './AppData'

export const getAppData = AppData.createQuery('getAppData', {
	$filter({ filters, options, params }) {
		if (params.filters) {
			Object.assign(filters, params.filters)
		}
		if (params.options) {
			Object.assign(options, params.options)
		}
		return { filters, options, params }
	},
	_id: 1,
	data: 1,
})

export const getAppData2 = AppData.createQuery('getAppData2', {
	$filter({ filters, options, params }) {
		if (params.filters) {
			Object.assign(filters, params.filters)
		}
		if (params.options) {
			Object.assign(options, params.options)
		}
		return { filters, options, params }
	},
	_id: 1,
	data: 1,
})
