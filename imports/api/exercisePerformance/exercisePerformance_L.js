import moment from 'moment-timezone'
import { ExercisePerformance } from './exercisePerformance'
import { Exercises } from '../exercises/exercises'
// Replace exercises
// Replace Exercises

ExercisePerformance.addLinks({
	user: {
		type: 'one',
		collection: Meteor.users,
		field: 'clientId',
		index: true,
	},
	exercise: {
		type: 'one',
		collection: Exercises,
		field: 'exerciseId',
		index: true,
	},
})

ExercisePerformance.addReducers({
	firstResponse: {
		body: {
			// Object, dependency graph
			first: {
				reps: 1,
				weight: 1,
				result: 1,
			},
		},
		reduce(object) {
			// return the value
			if (!object?.first) {
				return ''
			}
			// if (object.first?.reps || object.first?.weight|) {
			//     return object.first?.reps}x{lastBestFirst?.data?.first?.weight
			// }
			return object.first.result
		},
	},
	lastResponse: {
		body: {
			// Object, dependency graph
			last: {
				result: 1,
			},
		},
		reduce(object) {
			// return the value
			if (!object?.last) {
				return ''
			}
			return object.last.result
		},
	},
})
