import { Meteor } from 'meteor/meteor'
import { Email } from 'meteor/email'
import { check } from 'meteor/check'
import _ from 'lodash'
import { shareLinkTemplate } from './EmailTemplates/shareLinkTemplate'

const trelloSettings = Meteor.settings.private.trello

function md(strings, ...values) {
	const raw = strings.reduce((result, str, i) => {
		const value = values[i] || ''
		// Maneja los saltos de línea correctamente
		return result + str.replace(/\n\s+/g, '\n') + value
	}, '')

	// Elimina espacios en blanco extra y normaliza los saltos de línea
	return raw
		.trim()
		.replace(/^\s+/gm, '') // Elimina espacios al inicio de cada línea
		.replace(/\n{3,}/g, '\n\n') // Normaliza múltiples saltos de línea a máximo 2
		.replace(/\r\n/g, '\n') // Normaliza los saltos de línea
}

// Server: Define a method that the client can call.
Meteor.methods({
	async sendEmail(to, from, subject, text) {
		// Make sure that all arguments are strings.
		check([to, from, subject, text], [String])

		// Let other method calls from the same client start running, without
		// waiting for the email sending to complete.
		this.unblock()

		try {
			return await Email.sendAsync({ to, from, subject, text })
		} catch (err) {
			console.log(err)
			throw new Meteor.Error('email-failed', 'Failed to send email')
		}
	},

	// sendEmailWithTemplate({to, from, subject, template, data}) {
	// 	check( [to, from, subject], [String] );
	// 	check( template, Object );
	// 	check( data, Object );
	//
	// 	const from = '<EMAIL>';
	//
	// },

	async sendReportEmail({ audioUrl, eventsUrl, location }) {
		this.unblock()
		const { bugReportEmail, usersToAssign, tags } = trelloSettings
		const {
			_id: userId,
			profile: { firstName, lastName },
			username,
			emails,
		} = await Meteor.userAsync()

		const from = '<EMAIL>'
		const to = bugReportEmail

		const subject = `${firstName} ${lastName}, reports a new bug. ${_.map(tags, tag => `#${tag}`)} ${_.map(usersToAssign, user => `@${user}`)} `

		const text = md`
### New bug report from ${firstName}
Time: ${new Date().toLocaleString()}

- [Audio](${audioUrl})
- [Events](${eventsUrl})
- [Location](${location})

### User:
_id: ${userId}
username: ${username}
email: ${emails[0].address}
`

		return Email.sendAsync({ to, from, subject, text }).catch(err => {
			console.log(err)
			throw new Meteor.Error('email-failed', 'Failed to send email')
		})
	},

	async sendEmailLink(firstName, coachName, clientUrl, to) {
		this.unblock()
		// Make sure that all arguments are strings.
		check([firstName, coachName, clientUrl, to][String])

		const emailOptions = {
			to,
			from: '<EMAIL>',
			subject: `Hi ${firstName}, your plans have arrived.`,
			html: await shareLinkTemplate({ firstName, coachName, clientUrl }),
		}

		return await Email.sendAsync(emailOptions).catch(err => {
			console.log(err)
			throw new Meteor.Error('email-failed', 'Failed to send email')
		})
	},
})
