import { CoachClientsNotes, CoachClientsNotesSchema } from '../coachClientsNotes/coachClientsNotes'
import { ValidatedMethod } from 'meteor/mdg:validated-method'
import SimpleSchema from 'simpl-schema'
import moment from 'moment/moment'

export const coachClientsNotesInsert = new ValidatedMethod({
	name: 'coachClientsNotes.insert',
	validate: CoachClientsNotesSchema.validator(),
	async run(item) {
		if (!this.userId) {
			throw new Meteor.Error(
				'CoachClientsNotes.insert.notLoggedIn',
				'Must be logged in to create a questionnarie.',
			)
		}
		item.createdAt = moment().valueOf()
		return await CoachClientsNotes.insertAsync(item)
	},
})

export const coachClientsNotesUpdate = new ValidatedMethod({
	name: 'coachClientsNotes.update',
	validate: new SimpleSchema({
		_id: {
			type: String,
		},
		modifier: {
			type: Object,
			blackbox: true,
		},
	}).validator(),
	async run({ _id, modifier }) {
		if (!this.userId) {
			throw new Meteor.Error(
				'CoachClientsNotes.update.notLoggedIn',
				'Must be logged in to update a questionnaire.',
			)
		}
		return await CoachClientsNotes.updateAsync(_id, modifier)
	},
})

export const coachClientsNotesRemove = new ValidatedMethod({
	name: 'coachClientsNotes.remove',
	validate: new SimpleSchema({
		_id: {
			type: String,
		},
	}).validator(),
	async run({ _id }) {
		if (!this.userId) {
			throw new Meteor.Error(
				'CoachClientsNotes.remove.notLoggedIn',
				'Must be logged in to remove a questionnarie.',
			)
		}
		return await CoachClientsNotes.removeAsync(_id)
	},
})

export const coachClientsNotesPin = new ValidatedMethod({
	name: 'coachClientsNotes.pin',
	validate: new SimpleSchema({
		_id: {
			type: String,
			required: true,
		},
		clientId: {
			type: String,
			required: true,
		},
	}).validator(),
	async run({ _id, clientId }) {
		if (!this.userId) {
			throw new Meteor.Error('CoachClientsNotes.pin.notLoggedIn', 'Must be logged in to pin a note.')
		}

		const note = await CoachClientsNotes.findOneAsync(_id)
		if (!note) throw new Meteor.Error('CoachClientsNotes.pin.notFound', 'Note not found.')

		const pinnedNotesCount = CoachClientsNotes.countDocuments({
			pinned: true,
			clientId,
		})
		if (pinnedNotesCount >= 3 && !note.pinned)
			throw new Meteor.Error(
				'CoachClientsNotes.pin.limitReached',
				'You have reached the maximum limit of 3 pinned notes',
			)

		return await CoachClientsNotes.updateAsync(_id, {
			$set: { pinned: !note.pinned },
		})
	},
})
