import SimpleSchema from 'simpl-schema'
import moment from 'moment'
import { Devices } from '../devices/devices'

export const registerDevice = new ValidatedMethod({
	name: 'Device.register',
	validate: new SimpleSchema({
		deviceInfo: {
			type: Object,
			blackbox: true,
		},
		userId: {
			type: String,
			required: true,
		},
		deviceToken: {
			type: String,
			required: true,
		},
	}).validator(),
	async run({ deviceInfo, userId, deviceToken }) {
		const { ua, ...rest } = deviceInfo
		const existDevice = await Devices.findOneAsync({ userId, deviceToken })

		if (existDevice) {
			await Devices.removeAsync(existDevice._id)
		}

		const payload = {
			userId,
			deviceToken,
			lastAccess: moment().valueOf(),
			deviceDetails: {
				userAgent: ua,
				...rest,
			},
			createdAt: moment().valueOf(),
			updatedAt: moment().valueOf(),
		}
		return await Devices.insertAsync(payload)
	},
})

export const updateDevice = new ValidatedMethod({
	name: 'Device.update',
	validate: new SimpleSchema({
		userId: {
			type: String,
			required: true,
		},
		deviceToken: {
			type: String,
			required: true,
		},
	}).validator(),
	async run({ userId, deviceToken }) {
		const existDevice = await Devices.findOneAsync({ userId, deviceToken })
		if (existDevice) {
			await Devices.updateAsync(existDevice._id, {
				$set: {
					lastAccess: moment().valueOf(),
					updatedAt: moment().valueOf(),
				},
			})
		}
	},
})

export const syncDevices = new ValidatedMethod({
	name: 'Device.sync',
	validate: new SimpleSchema({
		userId: {
			type: String,
			required: true,
		},
		userType: {
			type: String,
			required: true,
		},
		deviceToken: {
			type: String,
			required: true,
		},
		deviceDetails: {
			type: Object,
			blackbox: true,
		},
		services: {
			type: Object,
			blackbox: true,
		},
	}).validator(),
	async run({ userId, userType, deviceToken, deviceDetails, services }) {
		const existDevice = await Devices.findOneAsync({ userId, deviceToken })
		// const existApp = await Devices.findOneAsync({userId, 'services.app': true});
		if (existDevice) {
			await Devices.updateAsync(existDevice._id, {
				$set: {
					lastAccess: moment().valueOf(),
					deviceDetails,
					services: {
						...existDevice.services,
						...services,
					},
					updatedAt: moment().valueOf(),
				},
			})
		} else {
			const payload = {
				userId,
				userType,
				deviceToken,
				lastAccess: moment().valueOf(),
				deviceDetails,
				services,
				createdAt: moment().valueOf(),
				updatedAt: moment().valueOf(),
			}
			await Devices.insertAsync(payload)
		}

		/**
		 * Send notification to coach when a client install the app in a new device
		 * Disabled for now
		 * existApp - check if the user has the app installed in any device and notify only in the first time
		 */

		// if(!services.app || existDevice?.services?.app || existApp) return;
		// if(userType === 'coach') {return}
		//
		// const client = Meteor.users.findOne(userId);
		// const coach = CoachClients.findOne({clientId: userId});
		//
		// const NewAppNotification = {
		// 	"notification": {
		// 		"title": 'App Installed',
		// 		"body": `${client?.profile?.firstName} ${client?.profile?.lastName} has installed your app in a new device`,
		// 	},
		// 	"senderId": userId,
		// 	"userIds": [ coach?.coachId ],
		// 	"url": Meteor.absoluteUrl(`app/client/edit/${userId}`),
		// 	"data": {
		// 		"userId": userId,
		// 		"url": Meteor.absoluteUrl(`app/client/edit/${userId}`),
		// 	},
		// }
		// console.log('NewAppNotification', NewAppNotification);
		//
		// HTTP.post(`${Meteor.settings.public.ampt.origin}/api/v2/notifications/send`, {
		// 	headers: {
		// 		'Content-Type': 'application/json',
		// 	},
		// 	data: NewAppNotification
		// }, (err, res) => {
		// 	if (err) {
		// 		console.log(err)
		// 	}
		// })
	},
})
