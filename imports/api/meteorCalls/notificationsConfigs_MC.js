import SimpleSchema from 'simpl-schema'
import { ValidatedMethod } from 'meteor/mdg:validated-method'
import { NotificationsConfigs } from '../notificationsConfigs/notificationsConfigs'

export const createConfigs = new ValidatedMethod({
	name: 'notificationsConfigs.create.legacy',
	validate: new SimpleSchema({
		userId: { type: String, optional: true },
	}).validator(),
	async run({ userId }) {
		const configs = await NotificationsConfigs.findOneAsync({ userId })
		if (configs) {
			return true
		}
		await NotificationsConfigs.insertAsync({
			userId,
			schemaVersion: 2,
			planCompleted: {
				inApp: true,
				push: false,
			},
			planSkipped: {
				inApp: true,
				push: false,
			},
			lastPlanCompleted: {
				inApp: true,
				push: false,
			},
			noPlansLeft: {
				inApp: true,
				push: false,
			},
			lowerLimitReached: {
				inApp: true,
				push: false,
			},
		})
		return true
	},
})

export const changePlansLimit = new ValidatedMethod({
	name: 'notificationsConfigs.changePlansLimit.legacy',
	validate: new SimpleSchema({
		_id: { type: String },
		lowerLimit: { type: Number },
	}).validator(),
	async run({ _id, lowerLimit }) {
		await NotificationsConfigs.updateAsync({ _id }, { $set: { lowerLimit } })
	},
})

export const upgradeConfigs = new ValidatedMethod({
	name: 'notificationsConfigs.upgradeConfigs',
	validate: new SimpleSchema({
		_id: { type: String },
	}).validator(),
	async run({ _id }) {
		const config = await NotificationsConfigs.findOneAsync({ _id })
		const modifier = {
			$set: {
				schemaVersion: 2,
				planCompleted: {
					inApp: config.planCompleted,
					push: config.planCompleted,
				},
				planSkipped: {
					inApp: config.planSkipped,
					push: config.planSkipped,
				},
				lastPlanCompleted: {
					inApp: config.lastPlanCompleted,
					push: config.lastPlanCompleted,
				},
				lowerLimitReached: {
					inApp: config.lowerLimitReached,
					push: config.lowerLimitReached,
				},
			},
		}
		await NotificationsConfigs.updateAsync({ _id: config._id }, modifier)
	},
})

export const toggleConfig = new ValidatedMethod({
	name: 'notificationsConfigs.toggleConfig',
	validate: new SimpleSchema({
		_id: { type: String },
		key: { type: String },
		subKey: {
			type: String,
			allowedValues: ['inApp', 'push'],
		},
	}).validator(),
	async run({ _id, key, subKey }) {
		const config = await NotificationsConfigs.findOneAsync({ _id })
		if (!config) {
			return Meteor.callAsync('notificationsConfigs.create', {
				userId: this.userId,
			})
		}
		if (!config.schemaVersion || config.schemaVersion < 2) {
			await Meteor.callAsync('notificationsConfigs.upgradeConfigs', {
				_id,
			})
		}
		const value = config[key][subKey] ?? config[key]
		const modifier = { $set: { [`${key}.${subKey}`]: !value } }
		await NotificationsConfigs.updateAsync({ _id }, modifier)
	},
})

export const unsubscribe = new ValidatedMethod({
	name: 'notificationsConfigs.unsubscribe',
	validate: new SimpleSchema({
		_id: { type: String },
		value: { type: Boolean },
	}).validator(),
	async run({ _id, value }) {
		await NotificationsConfigs.updateAsync({ _id }, { $set: { unsubscribeAll: value } })
	},
})
