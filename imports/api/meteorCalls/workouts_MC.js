import { ValidatedMethod } from 'meteor/mdg:validated-method'
import SimpleSchema from 'simpl-schema'
import _, { isEmpty, map, sortBy } from 'lodash'
import { ExercisePerformanceLogs } from '../exercisePerformanceLogs/exercisePerformanceLogs'
import { WorkoutDetails } from '../workoutDetails/workoutDetails'
import { Meteor } from 'meteor/meteor'
import { Workouts, WorkoutSchema } from '../workouts/workouts'
import { workoutAggSort } from '../workouts/workouts_A'
import moment from 'moment'
import { fillEmptyResults } from '@collections/meteorCalls/workoutDetails_MC'
import {
	sendPlanCompletedNotification,
	sendPlanSkippedNotification,
} from '@lib/QuickWebApp/Notifications/collections/build-notification-templates'

export const WorkoutClone = new ValidatedMethod({
	name: 'workout.clone',
	validate: WorkoutSchema.validator(),
	async run(item) {
		if (!this.userId) {
			throw new Meteor.Error('not-authorized')
		}
		const workoutId = await Workouts.insertAsync(item)
		return { workoutId }
	},
})

export const WorkoutUpdate = new ValidatedMethod({
	name: 'workout.update',
	validate: new SimpleSchema({
		_id: {
			type: String,
		},
		modifier: {
			type: Object,
			blackbox: true,
		},
		path: {
			type: String,
			optional: true,
		},
	}).validator({}),
	applyOptions: {
		noRetry: true,
	},
	async run({ _id, modifier }) {
		this.unblock()
		if (!this.userId) {
			throw new Meteor.Error('not-authorized')
		}
		return await Workouts.updateAsync(_id, modifier)
	},
})

export const WorkoutOrderUpdate = new ValidatedMethod({
	name: 'workout.orderUpdate',
	validate: new SimpleSchema({
		_id: {
			type: String,
		},
		newOrder: {
			type: Number,
			min: 1,
		},
	}).validator({ clean: true }),
	applyOptions: {
		noRetry: true,
	},
	async run({ _id, newOrder }) {
		this.unblock()
		if (!this.userId) {
			throw new Meteor.Error('not-authorized')
		}
		await Workouts.updateAsync(_id, { $set: { orderWorkout: newOrder } })
		return true
	},
})

export const workoutsTemplateRebalancedOrder = new ValidatedMethod({
	name: 'workouts.template.rebalancedOrder',
	validate: new SimpleSchema({
		templateId: {
			type: String,
			optional: false,
		},
	}).validator(),
	async run({ templateId }) {
		this.unblock()
		if (!this.userId) {
			throw new Meteor.Error('not-authorized')
		}

		const workouts = Workouts.find({ templateId }).fetchAsync()

		const sortedWorkouts = sortBy(workouts, 'orderWorkout')

		if (isEmpty(sortedWorkouts)) {
			return true
		}

		const bulkOps = map(sortedWorkouts, (workout, index) => {
			const orderWorkout = (index + 1) * 100000
			return {
				updateOne: {
					filter: { _id: workout._id },
					update: { $set: { orderWorkout } },
				},
			}
		})
		await Workouts.rawCollection().bulkWrite(bulkOps)
		return true
	},
})

export const workoutsClientRebalancedOrder = new ValidatedMethod({
	name: 'workouts.client.rebalancedOrder',
	validate: new SimpleSchema({
		clientId: {
			type: String,
			optional: false,
		},
	}).validator(),
	async run({ clientId }) {
		this.unblock()
		if (!this.userId) {
			throw new Meteor.Error('not-authorized', 'You are not authorized to perform this action')
		}

		const workouts = await Workouts.find({
			clientId,
			status: 'pending',
			creationStatus: 'READY',
		}).fetchAsync()

		if (isEmpty(workouts)) {
			return true
		}

		const sortedWorkouts = sortBy(workouts, 'orderWorkout')

		const bulkOps = map(sortedWorkouts, (workout, index) => {
			const orderWorkout = (index + 1) * 100000
			return {
				updateOne: {
					filter: { _id: workout._id },
					update: { $set: { orderWorkout } },
				},
			}
		})
		await Workouts.rawCollection().bulkWrite(bulkOps)
		return true
	},
})

export const WorkoutPublicUpdate = new ValidatedMethod({
	name: 'workout.public.update',
	validate: new SimpleSchema({
		_id: {
			type: String,
		},
		modifier: {
			type: Object,
			blackbox: true,
		},
	}).validator({}),
	async run({ _id, modifier }) {
		modifier.$set = _.pick(modifier.$set, ['status', 'statusCompleteDate'])
		if (!_.isEmpty(modifier.$set)) {
			return await Workouts.updateAsync(_id, modifier)
		} else {
			return false
		}
	},
})

export const WorkoutsMarkAsDone = new ValidatedMethod({
	name: 'workout.markAsDone',
	validate: new SimpleSchema({
		_id: {
			type: String,
		},
	}).validator({}),
	async run({ _id }) {
		this.unblock()
		const statusCompleteDate = moment().valueOf()
		const workout = await Workouts.findOneAsync({ _id })
		await Workouts.updateAsync(
			{ _id },
			{
				$set: {
					status: 'done',
					statusCompleteDate,
				},
			},
		)
		await fillEmptyResults({ workoutId: _id, clientId: workout.clientId })
		sendPlanCompletedNotification({ workoutId: _id, clientId: workout.clientId, coachId: workout.userId }).catch(
			error => {
				if (error.error === 'subscription-not-enabled') return
				console.log(error)
			},
		)
		return await Workouts.updateAsync(
			{ _id },
			{
				$set: {
					statusCompleteDate,
				},
			},
		)
	},
})

export const WorkoutsMarkAsSkipped = new ValidatedMethod({
	name: 'workout.markAsSkipped',
	validate: new SimpleSchema({
		_id: {
			type: String,
		},
	}).validator({}),
	async run({ _id }) {
		this.unblock()
		const workout = await Workouts.findOneAsync({ _id })
		await Workouts.updateAsync(
			{ _id },
			{
				$set: {
					status: 'skipped',
					statusCompleteDate: moment().valueOf(),
				},
			},
		)
		sendPlanSkippedNotification({ workoutId: _id, clientId: workout.clientId, coachId: workout.userId }).catch(
			error => {
				if (error.error === 'subscription-not-enabled') return
				console.log(error)
			},
		)
	},
})

export const workoutDelete = new ValidatedMethod({
	name: 'workout.remove',
	validate: new SimpleSchema({
		workoutId: {
			type: String,
		},
	}).validator({}),
	async run({ workoutId }) {
		if (!this.userId) {
			throw new Meteor.Error('not-authorized')
		}
		await ExercisePerformanceLogs.removeAsync({ workoutId: workoutId })
		await WorkoutDetails.removeAsync({ workoutId: workoutId })
		await Workouts.removeAsync({ _id: workoutId })
		return true
	},
})

export const assignWorkoutToTemplate = new ValidatedMethod({
	name: 'workout.assignWorkoutToTemplate',
	validate: new SimpleSchema({
		userId: {
			type: String,
		},
		workoutId: {
			type: String,
		},
		templateId: {
			type: String,
		},
	}).validator({}),
	applyOptions: {
		noRetry: true,
	},
	async run({ userId, workoutId, templateId }) {
		if (!this.userId) {
			throw new Meteor.Error('not-authorized')
		}
		const nextOrderWorkout = await getNextOrderWorkoutByFilters({
			templateId,
			creationStatus: 'READY',
		})
		return await Workouts.updateAsync(
			{ _id: workoutId },
			{
				$set: {
					templateId,
					userId,
					creationStatus: 'READY',
					orderWorkout: nextOrderWorkout,
				},
			},
		)
	},
})

export const publishWorkoutToClient = new ValidatedMethod({
	name: 'workout.publishWorkoutToClient',
	validate: new SimpleSchema({
		clientId: {
			type: String,
			optional: true,
		},
		workoutId: {
			type: String,
		},
	}).validator({}),
	async run({ workoutId, clientId }) {
		this.unblock()
		const userId = this.userId
		if (!userId) {
			throw new Meteor.Error('not-authorized')
		}
		const nextOrderWorkout = await getNextOrderWorkoutByFilters({
			clientId: clientId,
			userId,
		})
		return await Workouts.updateAsync(
			{ _id: workoutId },
			{
				$set: {
					creationStatus: 'READY',
					orderWorkout: nextOrderWorkout,
				},
			},
		)
	},
})

export const publishWorkout = new ValidatedMethod({
	name: 'workout.publishWorkout',
	validate: new SimpleSchema({
		workoutId: {
			type: String,
			optional: false,
		},
		templateId: {
			type: String,
			optional: false,
		},
	}).validator({}),
	async run({ workoutId, templateId }) {
		this.unblock()
		const userId = this.userId
		if (!userId) {
			throw new Meteor.Error('not-authorized')
		}
		const filters = {
			templateId,
			userId,
			creationStatus: 'READY',
		}
		const nextOrderWorkout = await getNextOrderWorkoutByFilters(filters)
		return await Workouts.updateAsync(
			{ _id: workoutId, userId },
			{
				$set: {
					creationStatus: 'READY',
					orderWorkout: nextOrderWorkout,
				},
			},
		)
	},
})

export const getWorkoutsAggSort = new ValidatedMethod({
	name: 'getWorkoutsAggSort',
	validate: new SimpleSchema({
		filters: {
			type: Object,
			optional: true,
			blackbox: true,
		},
		options: {
			type: Object,
			optional: true,
			blackbox: true,
		},
	}).validator({}),
	applyOptions: {
		noRetry: true,
	},
	async run({ filters, options }) {
		if (!this.userId) {
			throw new Meteor.Error('getWorkoutsAggSort', 'Must be logged.')
		}

		if (!Meteor.isServer) {
			return {
				data: [],
				count: 0,
				ready: true,
			}
		}

		const workouts = await Workouts.rawCollection().aggregate(workoutAggSort(filters, options)).toArray()

		return {
			data: workouts ?? [],
			count: (await Workouts.find(filters).countAsync()) ?? 0,
			ready: true,
		}
	},
})

export const publishWorkoutList = new ValidatedMethod({
	name: 'workout.publishWorkoutList',
	validate: new SimpleSchema({
		templateId: {
			type: String,
			optional: false,
		},
		planIds: {
			type: Array,
			optional: false,
		},
		'planIds.$': {
			type: String,
			optional: false,
		},
	}).validator({}),
	async run({ templateId, planIds }) {
		this.unblock()
		if (!this.userId) {
			throw new Meteor.Error('not-authorized')
		}
		const filters = {
			templateId,
			userId: this.userId,
			creationStatus: 'READY',
		}

		const selectedPlans = await Workouts.find({
			_id: { $in: planIds },
			templateId,
		}).fetchAsync()
		const sorted = selectedPlans.sort((a, b) => a.orderWorkout - b.orderWorkout)

		const nextOrderWorkout = await getNextOrderWorkoutByFilters(filters)

		const bulkOps = map(sorted, (plan, index) => {
			const orderWorkout = index * 100000 + nextOrderWorkout
			return {
				updateOne: {
					filter: { _id: plan._id },
					update: {
						$set: { creationStatus: 'READY', orderWorkout },
					},
				},
			}
		})

		await Workouts.rawCollection().bulkWrite(bulkOps)
		return true
	},
})

// ---- builder v2 methods ----

export const workoutToggleProperty = new ValidatedMethod({
	name: 'workout.toggleProperty',
	validate: new SimpleSchema({
		_id: {
			type: String,
		},
		field: {
			type: String,
		},
		value: {
			type: Boolean,
		},
	}).validator({}),
	async run({ _id, field, value }) {
		if (!this.userId) {
			throw new Meteor.Error('not-authorized')
		}
		const fieldsMap = {
			reps: 'repsField',
			sets: 'setsField',
			rest: 'restField',
			other: 'otherField',
			other1: 'otherField1',
			other2: 'otherField2',
		}
		const modifier = {
			$set: {
				[fieldsMap[field]]: value,
			},
		}
		return await Workouts.updateAsync(_id, modifier)
	},
})

const fieldSchema = new SimpleSchema({
	key: {
		type: String,
		allowedValues: ['reps', 'sets', 'rest', 'other', 'other1', 'other2'],
	},
	label: {
		type: String,
		optional: true,
	},
	enabled: {
		type: Boolean,
		optional: true,
	},
})

const fieldsLabelsMap = {
	reps: 'repsFieldLabel',
	sets: 'setsFieldLabel',
	rest: 'restFieldLabel',
	other: 'otherFieldLabel',
	other1: 'otherField1Label',
	other2: 'otherField2Label',
}

const fieldsEnabledMap = {
	reps: 'repsField',
	sets: 'setsField',
	rest: 'restField',
	other: 'otherField',
	other1: 'otherField1',
	other2: 'otherField2',
}

export const workoutEditProps = new ValidatedMethod({
	name: 'workout.editProps',
	validate: new SimpleSchema({
		_id: {
			type: String,
		},
		fields: {
			type: Array,
		},
		'fields.$': {
			type: fieldSchema,
		},
	}).validator({}),
	async run({ _id, fields }) {
		if (!this.userId) {
			throw new Meteor.Error('not-authorized')
		}

		let modifier = {
			$set: {},
		}

		_.each(fields, field => {
			if (field?.label) {
				modifier.$set[fieldsLabelsMap[field.key]] = field.label
			}
			if ('enabled' in field) {
				modifier.$set[fieldsEnabledMap[field.key]] = field.enabled
			}
		})

		return await Workouts.updateAsync(_id, modifier)
	},
})

export const workoutUpdateTitle = new ValidatedMethod({
	name: 'workout.updateTitle',
	validate: new SimpleSchema({
		_id: {
			type: String,
		},
		title: {
			type: String,
		},
	}).validator({}),
	async run({ _id, title }) {
		return await Workouts.updateAsync(_id, { $set: { title } })
	},
})

export const workoutUpdateNotes = new ValidatedMethod({
	name: 'workout.updateNotes',
	validate: new SimpleSchema({
		_id: {
			type: String,
		},
		notes: {
			type: String,
			optional: true,
		},
	}).validator({}),
	async run({ _id, notes = '' }) {
		this.unblock()
		return await Workouts.updateAsync(_id, { $set: { notes } })
	},
})

const getNextOrderWorkoutByFilters = async selectors => {
	const orderStep = 100000
	const lastDraftWorkout = await Workouts.findOneAsync(selectors, {
		sort: { orderWorkout: -1 },
	})

	if (!lastDraftWorkout || !lastDraftWorkout?.orderWorkout || typeof lastDraftWorkout?.orderWorkout !== 'number') {
		return orderStep
	}

	return lastDraftWorkout.orderWorkout + orderStep
}

Meteor.methods({
	async getPendingWorkouts(userId) {
		if (!this.userId) {
			throw new Meteor.Error('not-authorized')
		}
		return await Workouts.find({
			clientId: userId,
			status: 'pending',
		}).fetchAsync()
	},
})
