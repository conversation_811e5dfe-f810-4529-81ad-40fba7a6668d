import { ValidatedMethod } from 'meteor/mdg:validated-method'
import _ from 'lodash'
import {
	ClientCoachQuestionnaire,
	ClientCoachQuestionnaireSchema,
	SaveClientSurveySchema,
} from '../clientCoachQuestionnarie/clientCoachQuestionnarie'
import { sendSurveyNotification } from '@lib/QuickWebApp/Notifications/collections/build-notification-templates'

export const clientCoachQuestionnaireUpsert = new ValidatedMethod({
	name: 'clientCoachQuestionnaire.upsert',
	validate: ClientCoachQuestionnaireSchema.validator(),
	async run(item) {
		if (item._id) {
			const update = _.pick(item, ['responseQuestionnaire'])
			return await ClientCoachQuestionnaire.updateAsync({ _id: item._id }, { $set: update })
		} else {
			return await ClientCoachQuestionnaire.insertAsync(item)
		}
	},
})

export const saveClientSurveyResponse = new ValidatedMethod({
	name: 'clientCoachQuestionnaire.save',
	validate: SaveClientSurveySchema.validator(),
	async run(item) {
		const previousResponse = await ClientCoachQuestionnaire.findOneAsync({
			clientId: item.clientId,
			workoutId: item.workoutId,
			questionnaireId: item.questionnaireId,
		})
		try {
			if (previousResponse) {
				await ClientCoachQuestionnaire.updateAsync(previousResponse._id, { $set: item })
			} else {
				await ClientCoachQuestionnaire.insertAsync(item)
			}
		} catch (error) {
			console.error('Error saving client survey response', error)
			throw new Meteor.Error('saveClientSurveyResponse.error', 'Error saving client survey response', error)
		}
		sendSurveyNotification({
			workoutId: item.workoutId,
			clientId: item.clientId,
			coachId: item.coachId,
		}).catch(error => {
			if (error.error === 'subscription-not-enabled') return
			console.error('Error sending survey notification', error)
		})
		return true
	},
})
