import { NotificationTemplates } from './notificationTemplates'

export const getNotificationTemplates = NotificationTemplates.createQuery(
	'getNotificationTemplates',
	{
		$filter({ filters, options, params }) {
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},
		$paginate: true,
		_id: 1,
		trigger: 1,
		title: 1,
		message: 1,
		type: 1,
		senderType: 1,
		data: 1,
	},
	{},
)
