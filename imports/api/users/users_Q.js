export const getMyInfo = Meteor.users.createQuery(
	'getMyInfo',
	{
		$filter({ filters, options, params }) {
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},
		emails: 1,
		dateCreatedAt: 1,
		profile: {
			image_url: 1,
			image_public_id: 1,
			firstName: 1,
			lastName: 1,
			phone: 1,
		},
		roles: 1,
	},
	{ scoped: true },
)

export const getClientName = Meteor.users.createQuery(
	'getClientName',
	{
		$filter({ filters, options, params }) {
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},
		profile: {
			firstName: 1,
			lastName: 1,
		},
	},
	{},
)

export const getUser = Meteor.users.createQuery('getUser', {
	$filter({ filters, options, params }) {
		if (params.filters) {
			Object.assign(filters, params.filters)
		}
		if (params.options) {
			Object.assign(options, params.options)
		}
		return { filters, options, params }
	},
	profile: 1,
	roles: 1,
	emails: 1,
	createdAt: 1,
	services: 1,
	stripe: 1,
	lastPlans: 1,
	clientAppConfigs: {
		_id: 1,
		appName: 1,
		iconUrl: 1,
		iconPath: 1,
		themeColor: 1,
		backgroundColor: 1,
		settings: 1,
	},
	clients: {
		_id: 1,
		profile: {
			firstName: 1,
			lastName: 1,
			active: 1,
			email: 1,
		},
		clientConfig: {
			objective: 1,
			accessLink: 1,
			slug: 1,
			lastComment: 1,
		},
		workoutsClient: {
			_id: 1,
			statusCompleteDate: 1,
			status: 1,
			creationStatus: 1,
		},
		workoutsPending: {
			$filters: { status: 'pending', creationStatus: 'READY' },
			_id: 1,
		},
		lastWorkoutDone: {
			$filters: { status: 'done' },
			$options: { sort: { statusCompleteDate: -1 }, limit: 1 },
			_id: 1,
			statusCompleteDate: 1,
		},
		lastComment: {
			createdAt: 1,
		},
	},
})

export const getCustomers = Meteor.users.createQuery(
	'getCustomers',
	{
		$filter({ filters, options, params }) {
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},
		$paginate: true,
		createdAt: 1,
		profile: {
			firstName: 1,
			lastName: 1,
			active: 1,
		},
		workoutsPending: {
			$filters: { status: 'pending', creationStatus: 'READY' },
			_id: 1,
		},
		lastWorkoutDoneDate: {
			statusCompleteDate: 1,
		},
		lastCommentDate: {
			createdAt: 1,
		},
	},
	{ scoped: true },
)

export const getCustomersModal = Meteor.users.createQuery(
	'getCustomersModal',
	{
		$filter({ filters, options, params }) {
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},
		clients: {
			$filter({ filters, options, params }) {
				if (params?.clients?.filters) {
					Object.assign(filters, params.clients.filters)
				}
				if (params?.clients?.options) {
					Object.assign(options, params.clients.options)
				}
				return { filters, options, params }
			},
			_id: 1,
			profile: {
				firstName: 1,
				lastName: 1,
			},
		},
	},
	{ scoped: true },
)

export const getCustomersForAutoComplete = Meteor.users.createQuery(
	'getCustomersForAutoComplete',
	{
		$filter({ filters, options, params }) {
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},
		clients: {
			$filter({ filters, options, params }) {
				if (params?.clients?.filters) {
					Object.assign(filters, params.clients.filters)
				}
				if (params?.clients?.options) {
					Object.assign(options, params.clients.options)
				}
				return { filters, options, params }
			},
			_id: 1,
			profile: {
				firstName: 1,
				lastName: 1,
				active: 1,
			},
		},
	},
	{ scoped: true },
)

export const getCustomerDetailsClientActionPage = Meteor.users.createQuery(
	'getCustomerDetailsClientActionPage',
	{
		$filter({ filters, options, params }) {
			// console.log('filters: ', params);
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},
		emails: 1,
		profile: {
			image_url: 1,
			image_public_id: 1,
			firstName: 1,
			lastName: 1,
			phone: 1,
			active: 1,
			email: 1,
		},
	},
	{ scoped: true },
)

export const getCustomerDetailsWorkoutDetailsList = Meteor.users.createQuery(
	'getCustomerDetailsWorkoutDetailsList',
	{
		$filter({ filters, options, params }) {
			// console.log('filters: ', params);
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},
		emails: 1,
		dateCreatedAt: 1,
		profile: {
			image_url: 1,
			image_public_id: 1,
			firstName: 1,
			lastName: 1,
			phone: 1,
			active: 1,
			email: 1,
		},
		coachClients: {
			objective: 1,
			slug: 1,
		},
		workoutsClient: {
			$filter({ filters, options, params }) {
				if (params?.workouts?.filters) {
					Object.assign(filters, params.workouts.filters)
				}
				if (params?.workouts?.options) {
					Object.assign(options, params.workouts.options)
				}
				return { filters, options, params }
			},
			_id: 1,
			status: 1,
			title: 1,
			notes: 1,
			statusCompleteDate: 1,
			statusCompleteDateFormat: 1,
			creationStatus: 1,
			movementField: 1,
			setsField: 1,
			repsField: 1,
			tempoField: 1,
			restField: 1,
			rpeField: 1,
			orderWorkout: 1,
			otherField: 1,
			otherFieldLabel: 1,
			otherField1: 1,
			otherField1Label: 1,
			otherField2: 1,
			otherField2Label: 1,
			notesField: 1,
			clientId: 1,
			details: {
				_id: 1,
				movement: 1,
				order: 1,
				text: 1,
				part: 1,
				sets: 1,
				reps: 1,
				tempo: 1,
				rest: 1,
				rpe: 1,
				other: 1,
				other1: 1,
				other2: 1,
				notes: 1,
				superset: 1,
				suborder: 1,
			},
			questionaries: {
				_id: 1,
				responseQuestionnaire: 1,
				read: 1,
				workoutId: 1,
			},
		},
		workoutsDraft: 1,
		workoutsPending: {
			$filters: { status: 'pending', creationStatus: 'READY' },
			_id: 1,
		},
		workoutsDone: 1,
		workoutsSkipped: 1,
		workoutsWIP: 1,
		roles: 1,
		lastWorkoutDone: {
			$filters: { status: 'done' },
			$options: { sort: { statusCompleteDate: -1 }, limit: 1 },
			_id: 1,
			statusCompleteDate: 1,
		},
	},
	{ scoped: true },
)

export const getCustomerDetailsClientEditPage = Meteor.users.createQuery(
	'getCustomerDetailsClientEditPage',
	{
		$filter({ filters, options, params }) {
			// console.log('filters: ', params);
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},
		emails: 1,
		dateCreatedAt: 1,
		fullName: 1,
		profile: {
			image_url: 1,
			image_public_id: 1,
			firstName: 1,
			lastName: 1,
			phone: 1,
			active: 1,
			email: 1,
		},
		coachClients: {
			objective: 1,
			slug: 1,
		},
		workoutsClient: {
			$filter({ filters, options, params }) {
				if (params?.workouts?.filters) {
					Object.assign(filters, params.workouts.filters)
				}
				if (params?.workouts?.options) {
					Object.assign(options, params.workouts.options)
				}
				return { filters, options, params }
			},
			status: 1,
			creationStatus: 1,
		},
		clientConfig: {
			objective: 1,
			accessLink: 1,
			slug: 1,
			lastComment: 1,
		},
		workoutsDraft: 1,
		workoutsPending: {
			$filters: { status: 'pending', creationStatus: 'READY' },
			_id: 1,
		},
		workoutsDone: 1,
		workoutsSkipped: 1,
	},
	{ scoped: true },
)

export const getCustomerInfo = Meteor.users.createQuery('getCustomerInfo', {
	$filter({ filters, options, params }) {
		if (params.filters) {
			Object.assign(filters, params.filters)
		}
		if (params.options) {
			Object.assign(options, params.options)
		}
		return { filters, options, params }
	},
	profile: {
		firstName: 1,
		lastName: 1,
		active: 1,
		email: 1,
	},
	coachClients: {
		slug: 1,
	},
})
