import SimpleSchema from 'simpl-schema'

export const UserSchema = new SimpleSchema(
	{
		username: {
			type: String,
			optional: true,
		},
		heartbeat: {
			type: Date,
			optional: true,
		},
		services: {
			type: Object,
			optional: true,
			blackbox: true,
		},
		roles: {
			type: Object,
			optional: true,
			blackbox: true,
		},
		emails: {
			type: Array,
			optional: true,
		},
		'emails.$': {
			type: Object,
			blackbox: true,
		},
		createdAt: {
			type: Date,
			optional: true,
		},
		userIds: {
			type: Array,
			optional: true,
		},
		'userIds.$': {
			type: String,

			optional: true,
		},
		profile: {
			type: Object,
			optional: true,
		},
		'profile.image_url': {
			type: String,
			optional: true,
		},
		'profile.image_public_id': {
			type: String,
			optional: true,
		},
		'profile.firstName': {
			type: String,
			optional: true,
		},
		'profile.lastName': {
			type: String,
			optional: true,
		},
		'profile.isNew': {
			type: Boolean,
			optional: true,
			defaultValue: true,
		},
		'profile.active': {
			type: Boolean,
			optional: true,
			defaultValue: true,
		},
		'profile.coachUsername': {
			type: String,
			optional: true,
		},
		'profile.phone': {
			type: String,
			optional: true,
		},
		convertKitId: {
			type: String,
			optional: true,
		},
		stripe: {
			type: Object,
			optional: true,
		},
		management: {
			type: String,
			optional: true,
			defaultValue: 'none',
		},
		'stripe.customerId': {
			type: String,
			optional: true,
		},
		'stripe.subscriptionId': {
			type: String,
			optional: true,
		},
		'stripe.status': {
			type: String,
			optional: true,
			defaultValue: 'free',
		},
		'stripe.lastCharge': {
			type: String,
			optional: true,
		},
		subscription: {
			type: Object,
			optional: true,
		},
		'subscription.planId': {
			type: String,
			optional: true,
		},
	},
	{},
)

export const UserFormSchema = UserSchema.pick('profile.firstName', 'profile')

export const UserFormSchema2 = new SimpleSchema(
	{
		profile: {
			type: Object,
			optional: true,
		},
		'profile.firstName': {
			type: String,
			optional: true,
		},
		'profile.lastName': {
			type: String,
			optional: true,
		},
		'profile.phone': {
			type: String,
			optional: true,
		},
		'profile.countryCode': {
			type: String,
			optional: true,
		},
	},
	{},
)

export const UserClientSchema = new SimpleSchema(
	{
		firstName: {
			type: String,
			optional: false,
			uniforms: {
				variant: 'outlined',
			},
		},
		lastName: {
			type: String,
			optional: false,
			uniforms: {
				variant: 'outlined',
			},
		},
		phone: {
			type: String,
			optional: true,
			uniforms: {
				variant: 'outlined',
			},
		},
		email: {
			type: String,
			optional: false,
			uniforms: {
				variant: 'outlined',
			},
		},
		objective: {
			type: String,
			optional: true,
			uniforms: {
				variant: 'outlined',
			},
		},
	},
	{},
)

export const UserCoachFormSchema = new SimpleSchema(
	{
		firstName: {
			type: String,
			optional: false,
			uniforms: {
				variant: 'outlined',
			},
		},
		lastName: {
			type: String,
			optional: false,
			uniforms: {
				variant: 'outlined',
			},
		},
		phone: {
			type: String,
			optional: true,
			uniforms: {
				variant: 'outlined',
			},
		},
		email: {
			type: String,
			optional: true,
			uniforms: {
				variant: 'outlined',
			},
		},
	},
	{},
)
