import moment from 'moment-timezone'
import { Template } from './template'
import { Workouts } from '../workouts/workouts'
import { WorkoutDetails } from '../workoutDetails/workoutDetails'

Template.addLinks({
	coach: {
		type: 'one',
		collection: Meteor.users,
		field: 'coachId',
		index: true,
	},
	workouts: {
		collection: Workouts,
		inversedBy: 'template',
	},
})

Template.addReducers({
	dateCreatedAt: {
		body: {
			// Object, dependency graph
			createdAt: 1,
		},
		reduce(object) {
			// return the value
			return object.createdAt
		},
	},
	workoutsDraft: {
		body: {
			workouts: 1,
		},
		reduce(object) {
			return object && Array.isArray(object.workouts)
				? object.workouts.filter(workout => workout.creationStatus === 'DRAFT' && workout.status === 'pending')
				: []
		},
	},
	workoutsReady: {
		body: {
			workouts: 1,
		},
		reduce(object) {
			return object && Array.isArray(object.workouts)
				? object.workouts.filter(workout => workout.creationStatus === 'READY' && workout.status === 'pending')
				: []
		},
	},
})
