import { Mongo } from 'meteor/mongo'
import SimpleSchema from 'simpl-schema'
import _ from 'lodash'

export const ClientAppConfigs = new Mongo.Collection('clientAppConfigs')

ClientAppConfigs.deny({
	insert: function () {
		return false
	},
	update: function () {
		return false
	},
	remove: function () {
		return false
	},
})

export const ClientAppConfigsSchema = new SimpleSchema({
	_id: {
		type: String,
		optional: true,
	},
	coachId: {
		type: String,
		optional: true,
	},
	appName: {
		type: String,
		optional: true,
		defaultValue: 'QuickCoach',
	},
	coachUsername: {
		type: String,
		optional: true,
	},
	iconUrl: {
		type: String,
		optional: true,
	},
	iconPath: {
		type: String,
		optional: true,
		defaultValue: 'resources/brand/main-logo-512',
	},
	themeColor: {
		type: String,
		optional: true,
		min: 7,
		max: 7,
		defaultValue: '#033598',
	},
	backgroundColor: {
		type: String,
		optional: true,
		min: 7,
		max: 7,
		defaultValue: '#ffffff',
	},
	pdfColor: {
		type: String,
		optional: true,
	},
	isActiveSubscription: {
		type: Boolean,
		optional: true,
		defaultValue: false,
	},
	settings: {
		type: Object,
		optional: true,
		blackbox: true,
		defaultValue: {
			print: {
				colors: {
					supersetRow: {
						type: 'themeColor',
						value: '#000000',
					},
				},
			},
			email: {
				colors: {
					header: {
						type: 'backgroundColor',
						value: '#000000',
					},
					title: {
						type: 'themeColor',
						value: '#000000',
					},
					button: {
						type: 'themeColor',
						value: '#000000',
					},
				},
			},
			pwa: {
				colors: {
					header: {
						type: 'themeColor',
						value: '#000000',
					},
					body: {
						type: 'themeColor',
						value: '#000000',
					},
					text: {
						type: 'themeColor',
						value: '#000000',
					},
				},
			},
		},
	},
})

export const subscriptionSchema = new SimpleSchema(_.pick(ClientAppConfigsSchema, ['coachId', 'isActiveSubscription']))

ClientAppConfigs.attachSchema(ClientAppConfigsSchema)
