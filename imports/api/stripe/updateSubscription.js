import { Meteor } from 'meteor/meteor'
import Stripe from '/imports/startup/services/Stripe'
import getStripeAccount from '/imports/api/stripe/getStripeAccount'
import getStripeCustomerId from './customer/getStripeCustomerId'
import { Plans } from '../plans/plans'
import { Coupons } from '../coupons/coupons'

export default async (userId, subscriptionId, planId, quantity, couponId) => {
	try {
		const stripeCustomerId = await getStripeCustomerId(userId)

		plan = await Plans.findOneAsync({ _id: planId })

		const stripeAccount = await getStripeAccount()

		if (!stripeAccount) {
			throw new Error(400, 'Box does not have a stripe account')
		}

		subscriptionProps = {
			plan: plan.stripe_id,
			quantity: parseInt(quantity, 10),
		}

		if (couponId) {
			coupon = Coupons.findOneAsync({ _id: couponId })
			subscriptionProps.coupon = coupon?.stripe_id
		}

		subscription = await Stripe.customers.updateSubscription(stripeCustomerId, subscriptionId, subscriptionProps, {
			stripeAccount,
		})

		return subscription
	} catch (error) {
		throw new Meteor.Error('Unable to update Subscription')
	}
}
