import { Meteor } from 'meteor/meteor'
import getCustomer from './customer/getCustomer'
import getCustomerSubscriptions from './getCustomerSubscriptions'
import Stripe from '/imports/startup/services/Stripe'

export default async (invoiceItem, userId) => {
	try {
		const customer = await getCustomer(userId)
		const subscriptions = await getCustomerSubscriptions(userId, 'active')
		Object.assign(customer, { subscriptions })

		// const user = Meteor.users.findOne( { _id: userId })
		let validSubscription = false
		if (customer.subscriptions?.length > 0) {
			validSubscription = _.chain(customer.subscriptions)
				.sortBy(subscription => subscription.current_period_end)
				.find(subscription => _.indexOf(['day', 'week', 'month'], subscription.plan.interval) >= 0)
				.value()
		}

		if (validSubscription) {
			// Subscription Add Invoice Item
			invoiceItem = await Stripe.invoiceItems.create({
				customer: customer.id,
				amount: invoiceItem.amount,
				currency: 'usd',
				description: invoiceItem.description,
				metadata: invoiceItem.metadata,
				subscription: validSubscription.id,
			})

			// AddPush "Item Added", `Item Added to ${user.profile.firstName}","${numeral(invoiceItem.amount/100).format('$0,0.00')} - ${invoiceItem.description}`

			return invoiceItem.id
		} else if (customer.sources?.total_count > 0) {
			// Charge Item Now if has payment source
			charge = await Stripe.charges.create({
				customer: customer.id,
				amount: invoiceItem.amount,
				currency: 'usd',
				description: invoiceItem.description,
				metadata: invoiceItem.metadata,
			})

			// AddPush "Item Charged", `Charged ${user.profile.firstName}","${numeral(invoiceItem.amount/100).format('$0,0.00')} - ${invoiceItem.description}`
			return charge.id
		} else {
			// AddPush "Item Not Charged", `Not Paid Billing Error ${user.profile.firstName}","${numeral(invoiceItem.amount/100).format('$0,0.00')} - ${invoiceItem.description}`
			throw new Meteor.Error('Unable to add invoice item')
		}
	} catch (err) {
		// console.log(err)
		// AddPush "Item Not Charged", `Not Paid Stripe Error ${user.profile.firstName}","${numeral(invoiceItem.amount/100).format('$0,0.00')} - ${invoiceItem.description}`
		throw new Meteor.Error(`Unable to add invoice item: ${err?.raw?.message}`)
	}
}
