import { Meteor } from 'meteor/meteor'
import Stripe from '/imports/startup/services/Stripe'
import getStripeCustomerId from './customer/getStripeCustomerId'

// Subscription Statuses
// active
// past_due
// unpaid
// canceled
// incomplete
// incomplete_expired
// trialing
// all
// ended

export default async (userId, status = 'all') => {
	try {
		const stripeCustomerId = await getStripeCustomerId(userId)
		const payload = {
			customer: stripeCustomerId,
			status: status, // all active past_due unpaid canceled incomplete incomplete_expired trialing ended
		}
		const subscriptions = await Stripe.subscriptions.list(payload)
		return subscriptions?.data
	} catch (err) {
		throw new Meteor.Error(`No Subscriptions: ${err.raw.message}`)
	}
}
