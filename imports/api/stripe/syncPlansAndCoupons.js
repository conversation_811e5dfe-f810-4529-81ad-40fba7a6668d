import { Random } from 'meteor/random'
import Stripe from '../../../imports/startup/services/Stripe'
import DB from '../../../imports/startup/graphql/config/db'

export default async () => {
	try {
		const db = await DB()

		plansBulk = db.collection('plans').initializeUnorderedBulkOp()
		let plansBulkHasOperations = false

		couponsBulk = db.collection('coupons').initializeUnorderedBulkOp()
		let couponsBulkHasOperations = false

		taxesBulk = db.collection('taxes').initializeUnorderedBulkOp()
		let taxesBulkHasOperations = false

		await Stripe.taxRates.list({ limit: 100 }).autoPagingEach(tax => {
			taxesBulkHasOperations = true
			taxesBulk
				.find({ stripe_id: tax.id })
				.upsert()
				.updateOne({
					$set: {
						active: tax.active,
						createdAt: tax.created,
						description: tax.description,
						display_name: tax.display_name,
						inclusive: tax.inclusive,
						percentage: tax.percentage,
					},
					$setOnInsert: {
						_id: Random.id(),
					},
				})
		})
		await Stripe.plans.list({ limit: 100 }).autoPagingEach(plan => {
			plansBulkHasOperations = true
			plansBulk
				.find({ stripe_id: plan.id })
				.upsert()
				.updateOne({
					$set: {
						amount: plan.amount,
						currency: plan.currency,
						interval: plan.interval,
						interval_count: plan.interval_count,
						name: plan.nickname || plan.metadata.name,
						description: plan.metadata.description,
						serviceDescription: plan.statement_descriptor,
					},
					$setOnInsert: {
						_id: Random.id(),
						enabled: true,
					},
				})
		})

		await Stripe.coupons.list({ limit: 100 }).autoPagingEach(coupon => {
			couponsBulkHasOperations = true
			couponsBulk
				.find({ stripe_id: coupon.id })
				.upsert()
				.updateOne({
					$set: {
						duration: coupon.duration,
						currency: coupon.currency || 'usd',
						amount_off: coupon.amount_off,
						percent_off: coupon.percent_off,
						max_redemptions: coupon.max_redemptions,
						duration_in_months: coupon.duration_in_months,
						times_redeemed: coupon.times_redeemed,
						redeem_by: coupon.redeem_by,
						valid: coupon.valid,
					},
					$setOnInsert: {
						_id: Random.id(),
						enabled: true,
					},
				})
		})

		executeTaxes = () => new Promise(resolve => taxesBulk.execute(resolve()))

		executePlans = () => new Promise(resolve => plansBulk.execute(resolve()))

		executeCoupons = () => new Promise(resolve => couponsBulk.execute(resolve()))

		await Promise.all([
			...(taxesBulkHasOperations ? [executeTaxes()] : []),
			...(plansBulkHasOperations ? [executePlans()] : []),
			...(couponsBulkHasOperations ? [executeCoupons()] : []),
		])
		return true
	} catch (err) {
		throw err
	}
}
