import { Meteor } from 'meteor/meteor'
import Stripe from '/imports/startup/services/Stripe'
import getStripeCustomerId from './customer/getStripeCustomerId'
import { Plans } from '../plans/plans'
import { Coupons } from '../coupons/coupons'
import { Subscriptions } from '../subscriptions/subscriptions'
import moment from 'moment'

export default async ({ userId, planId, quantity, couponId, trialEnd, autoCharge = true }) => {
	const customerId = await getStripeCustomerId(userId)

	const plan = await Plans.findOneAsync({ _id: planId })

	if (!plan) {
		throw new Meteor.Error('Plan not found')
	}

	const subscriptionProps = {
		customer: customerId,
		collection_method: autoCharge ? 'charge_automatically' : 'send_invoice',
		items: [
			{
				price: plan.stripe_id,
				quantity: parseInt(quantity, 10),
			},
		],
	}
	if (!autoCharge) {
		subscriptionProps.days_until_due = 1
	}

	if (couponId) {
		const coupon = Coupons.findOneAsync({ _id: couponId })
		subscriptionProps.coupon = coupon?.stripe_id
	}

	if (trialEnd) {
		subscriptionProps.trial_end = trialEnd
	}

	const subscription = await Stripe.subscriptions.create(subscriptionProps)

	await Subscriptions.insertAsync({
		userId: userId,
		customerId,
		subscriptionId: subscription.id,
		planId,
		couponId,
		createdAt: moment().valueOf(),
	})
	if (!autoCharge) {
		await Stripe.invoices.finalizeInvoice(subscription.latest_invoice)
	}

	return subscription
}
