import Stripe from '/imports/startup/services/Stripe'
import getStripeAccount from '/imports/api/stripe/getStripeAccount'

export default async invoiceId => {
	try {
		const stripeAccount = await getStripeAccount()

		if (stripeAccount) {
			return await Stripe.invoices.finalizeInvoice(invoiceId, {
				stripeAccount,
			})
		} else {
			throw new Error(400, 'Box does not have a stripe account')
		}
	} catch (err) {
		return new Error(`Unable to finalize invoice: ${err.raw.message}`)
	}
}
