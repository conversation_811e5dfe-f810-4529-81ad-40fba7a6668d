import { Meteor } from 'meteor/meteor'
import Stripe from '/imports/startup/services/Stripe'
import getStripeAccount from '/imports/api/stripe/getStripeAccount'
import getStripeCustomerId from './customer/getStripeCustomerId'

export default async (userId, boxId) => {
	try {
		const stripeCustomerId = await getStripeCustomerId(userId)

		const stripeAccount = await getStripeAccount()

		if (!stripeAccount) {
			throw new Error(400, 'Box does not have a stripe account')
		}

		invoices = await Stripe.invoices.list(
			{
				customer: stripeCustomerId,
			},
			{ stripeAccount },
		)

		return invoices
	} catch (err) {
		return []
	}
}
