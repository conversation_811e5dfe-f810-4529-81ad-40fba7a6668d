// import _ from 'lodash'
// import { findPlan } from '../../api/meteorCalls/users_MC'
// import addInvoiceItem from './addInvoiceItem'
// import addPaymentMethod from './addPaymentMethod'
// import addSubscriptionStripeCustomer from './addSubscriptionStripeCustomer'
// import billInvoice from './billInvoice'
// import chargeCardUsingCheckout from './chargeCardUsingCheckout'
// import checkIfCustomerHasCard from './checkIfCustomerHasCard'
// import checkStripeConnect from './checkStripeConnect'
// import createCustomerId from './createCustomerId'
import customerCheckoutSession from './customerCheckoutSession'
import customerPortalLink from './customerPortalLink'
// import finalizeInvoice from './finalizeInvoice'
// import getCustomer from './getCustomer'
import getCustomerSubscriptions from './getCustomerSubscriptions'
// import getPastChargesForCustomer from './getPastChargesForCustomer'
// import getPastInvoicesForCustomer from './getPastInvoicesForCustomer'
// import getStripeAccount from './getStripeAccount'
// import getStripeCustomerId from './getStripeCustomerId'
// import getUpcomingInvoiceForSubs from './getUpcomingInvoiceForSubs'
// import getUserIdwithCustomerId from './getUserIdwithCustomerId'
// import payInvoiceWithCash from './payInvoiceWithCash'
// import removeInvoiceItem from './removeInvoiceItem'
// import removeSubscription from './removeSubscription'
// import sendInvoice from './sendInvoice'
// import showFunding from './showFunding'
import syncPlansAndCoupons from './syncPlansAndCoupons'
import updateCustomerEmail from './updateCustomerEmail'
import createCustomerId from './customer/createCustomerId'
// import updateAccountBalance from './updateAccountBalance'
// import updateBillingDate from './updateBillingDate'
// import updateCreditCard from './updateCreditCard'
// import updateSubscription from './updateSubscription'
// import voidInvoice from './voidInvoice'
import { Roles } from 'meteor/alanning:roles'
import getCustomer from './customer/getCustomer'
import deleteCustomer from './customer/deleteCustomer'
import getStripeCustomerId from './customer/getStripeCustomerId'
import stripe from '../../startup/services/Stripe'
import { Meteor } from 'meteor/meteor'
import moment from 'moment'

Meteor.methods({
	// ---- Customer Methods ----
	async 'stripe.createCustomer'({ userId, clockId }) {
		this.unblock()
		// Check if called by server if not check if admin to allow
		if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) {
			return
		}
		return createCustomerId(userId, clockId)
	},
	async 'stripe.getCustomer'({ userId }) {
		this.unblock()
		// Check if called by server if not check if user
		if (this.connection && !this.userId) {
			return
		}
		return getCustomer(userId)
	},
	async 'stripe.deleteCustomer'({ userId }) {
		this.unblock()
		// Check if called by server if not check if user
		if (this.connection && !this.userId) {
			return
		}
		return deleteCustomer(userId)
	},

	// ---- Subscription Methods ----
	async 'stripe.createSubscription'({ userId, planId, settings }) {
		this.unblock()
		// Check if called by server if not check if admin to allow
		if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) {
			return
		}
		const customerId = await getStripeCustomerId(userId)
		const subscription = await stripe.subscriptions.create({
			customer: customerId,
			items: [{ price: planId }],
			...settings,
		})
		await Meteor.users.updateAsync(
			{ _id: userId },
			{
				$set: {
					'stripe.subscriptionId': subscription.id,
					'stripe.status': subscription.status,
				},
			},
		)
		return subscription
	},
	async 'stripe.createTrialSubscription'({ planId, settings }) {
		this.unblock()
		// Check if called by server if not check if admin to allow
		if (!this.userId) {
			return console.error('User not found')
		}

		const { trial_days = 21 } = Meteor.settings.private.stripe

		const user = await Meteor.users.findOneAsync(
			{ _id: this.userId },
			{ fields: { emails: 1, profile: 1, stripe: 1 } },
		)

		const trialClaimed = 'stripe' in user && 'trialStart' in user.stripe
		if (trialClaimed) {
			return
		}

		const customerId = await getStripeCustomerId(this.userId)

		try {
			const subscription = await stripe.subscriptions.create({
				customer: customerId,
				items: [{ price: planId }],
				trial_end: moment().add(trial_days, 'days').add(5, 'minutes').unix(),
				trial_settings: {
					end_behavior: {
						missing_payment_method: 'cancel',
					},
				},
				metadata: {
					name: user.profile.firstName,
					userId: this.userId,
				},
				...settings,
			})
			await Meteor.users.updateAsync(
				{ _id: this.userId },
				{
					$set: {
						'stripe.subscriptionId': subscription.id,
						'stripe.status': subscription.status,
						'stripe.trialEnd': subscription.trial_end,
						'stripe.trialStart': subscription.trial_start,
					},
				},
			)
			return subscription
		} catch (e) {
			console.log('error', e)
		}
	},

	async 'stripe.getSubscriptions'({ userId, status }) {
		this.unblock()
		// Check if called by server if not check if user
		if (this.connection && !this.userId) {
			return
		}
		return getCustomerSubscriptions(userId, status)
	},
	async 'stripe.cancelSubscription'({ userId }) {
		this.unblock()
		// Check if called by server if not check if user
		const isAdmin = await Roles.userIsInRoleAsync(this.userId, ['admin'])

		if ((this.connection && !this.userId) || !isAdmin) {
			return
		}
		const user = await Meteor.users.findOneAsync({ _id: userId })
		console.log(user)

		const subscriptionId = user.stripe.subscriptionId

		const subscription = await stripe.subscriptions.cancel(subscriptionId)
		await Meteor.users.updateAsync(
			{ _id: userId },
			{
				$unset: {
					'stripe.subscriptionId': 1,
					'stripe.trialEnd': 1,
					'stripe.trialStart': 1,
					subscription: 1,
				},
				$set: { 'stripe.status': 'free' },
			},
		)
		return subscription
	},

	// async "addInvoiceItem" (invoiceItem, userId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await addInvoiceItem(invoiceItem, userId)
	// },
	// async "addPaymentMethod" (source, userId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await addPaymentMethod(source, userId)
	// },
	// async "addSubscriptionStripeCustomer" ( userId, planId, quantity, couponId, date, autocharge=true) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await addSubscriptionStripeCustomer( userId, planId, quantity, couponId, date,autocharge)
	// },
	// async "billInvoice" (invoiceId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return await billInvoice(invoiceId)
	// },
	// async "chargeCardUsingCheckout" (res, charge) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await chargeCardUsingCheckout(res, charge)
	// },
	// async "checkIfCustomerHasCard"( userId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await checkIfCustomerHasCard( userId)
	// },
	// async "checkStripeConnect" () {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return await checkStripeConnect()
	// },

	async customerPortalLink(userId, path) {
		this.unblock()
		// Check if called by server if not check if admin to allow
		if (this.connection && !this.userId) {
			return
		}
		return customerPortalLink(userId, path)
	},
	async customerCheckoutSession(payload) {
		this.unblock()
		// Check if called by server if not check if admin to allow
		if (this.connection && !this.userId) {
			return
		}
		return customerCheckoutSession(payload)
	},
	async updateCustomerEmail(userId, email) {
		this.unblock()
		if (this.connection && !this.userId) {
			return
		}
		return updateCustomerEmail(userId, email)
	},
	// async "finalizeInvoice" (invoiceId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return await finalizeInvoice(invoiceId)
	// },

	async getCustomerSubscriptions(userId, status) {
		this.unblock()
		// Check if called by server if not check if user
		if (this.connection && !this.userId) {
			return
		}
		return getCustomerSubscriptions(userId, status)
	},
	// async "getPastChargesForCustomer" (userId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await getPastChargesForCustomer(userId)
	// },
	// async "getPastInvoicesForCustomer" (userId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await getPastInvoicesForCustomer(userId)
	// },
	// async "getStripeAccount" (userId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return await getStripeAccount(userId)
	// },
	// async "getStripeCustomerId" (userId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await getStripeCustomerId(userId)
	// },
	// async "getUpcomingInvoiceForSubs" (userId, subsId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await getUpcomingInvoiceForSubs(userId,  subsId)
	// },
	// "getUserIdwithCustomerId" (customerId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return getUserIdwithCustomerId( userId)
	// },
	// async "payInvoiceWithCash" (invoiceId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return await payInvoiceWithCash(invoiceId)
	// },
	// async "removeInvoiceItem" (userId, invoiceItemId, description) {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return await removeInvoiceItem(userId, invoiceItemId, description)
	// },
	// async "removeSubscription" ( userId, subsId, atEnd) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await removeSubscription( userId, subsId, atEnd)
	// },
	// async "sendInvoice" (invoiceId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return await sendInvoice(invoiceId)
	// },
	// async "showFunding" ( userId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await showFunding( userId)
	// },
	async syncPlansAndCoupons() {
		this.unblock()
		// Check if called by server if not check if admin to allow
		if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) {
			return
		}
		if (!Roles.userIsInRole(this.userId, ['admin'])) {
			return
		}
		return syncPlansAndCoupons()
	},
	// async "updateAccountBalance" (userId, amountInPennies) {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return await updateAccountBalance(userId, amountInPennies)
	// },
	// async "updateBillingDate" (subscriptionId, userId,  unix, prorate, amount) {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return await updateBillingDate(subscriptionId, userId,  unix, prorate, amount)
	// },
	// async "updateCreditCard" (token, userId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if user
	// 	if (this.connection && !this.userId) { return }
	// 	return await updateCreditCard(token, userId)
	// },
	// async "updateSubscription" ( userId, subsId, planId, quantity, couponId) {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return await updateSubscription( userId, subsId, planId, quantity, couponId)
	// },
	// async "voidInvoice" (invoiceId)  {
	// 	this.unblock()
	// 	// Check if called by server if not check if admin to allow
	// 	if (this.connection && !Roles.userIsInRole(this.userId, ['admin'])) { return }
	// 	return await voidInvoice(invoiceId)
	// },
	// async 'userTicketQuantity.fetch' (userId){
	// 	let customer = Meteor.users.findOne({ _id:userId })
	// 	const subscriptions =  await getCustomerSubscriptions(userId ,"active")
	// 	if (_.isEmpty(customer?.subscription)) {
	// 		//this runs if customer does not yet have a subscription and selects one
	// 		if (subscriptions.length > 0) {
	// 			Roles.addUsersToRoles(userId, "customer")
	// 			editProfileTag({userId , key:"role" , value: "customer" , insert:true })
	// 			const sub = findPlan(subscriptions[0].plan.id)
	// 			Meteor.users.update({_id:customer._id},{ $set: {subscription:sub }})
	// 			return sub?.ticketMax
	// 		} else {
	// 			return false
	// 		}
	// 		return customer?.subscription?.ticketMax
	// 	}else{
	// 		//this runs if customer already has a subscription and wants to update
	// 		const sub = findPlan(subscriptions[0].plan.id)
	// 		Meteor.users.update({_id:customer._id},{ $set: {subscription:sub }})
	// 		return sub?.ticketMax
	//
	// 	}
	// }
})
