import { Meteor } from 'meteor/meteor'
import Stripe from '/imports/startup/services/Stripe'
import getStripeCustomerId from './customer/getStripeCustomerId'

export default async userId => {
	try {
		const stripeCustomerId = await getStripeCustomerId(userId)

		if (!stripeCustomerId) {
			throw new Error(400, 'User does not have a stripe account')
		}

		sources = await Stripe.customers.listSources(stripeCustomerId)

		return sources?.data?.length > 0
	} catch (err) {
		throw new Meteor.Error(`Customer has no card: ${err.raw.message}`)
	}
}
