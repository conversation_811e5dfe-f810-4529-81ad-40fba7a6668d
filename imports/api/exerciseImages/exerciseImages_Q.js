import { ExerciseImages } from './exerciseImages'

export const getExerciseImages = ExerciseImages.createQuery(
	'getExerciseImages',
	{
		$filter({ filters, options, params }) {
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},

		url: 1,
		publicId: 1,
		isUsedOnWorkout: 1,
		createdAt: 1,
		updatedAt: 1,
	},
	{},
)
