import { ExercisePerformanceLogs } from './exercisePerformanceLogs'
import { Exercises } from '../exercises/exercises'
import { ExercisePerformance } from '../exercisePerformance/exercisePerformance'

ExercisePerformanceLogs.after.insert(async function (userId, doc) {
	const { exerciseId, clientId } = doc
	if (!exerciseId) return

	const exercise = await Exercises.findOneAsync({ _id: exerciseId })
	if (!exercise) return

	doc.exerciseName = exercise.text

	const performance = await ExercisePerformance.findOneAsync({
		exerciseId,
		clientId,
	})
	if (!performance) {
		const first =
			(await ExercisePerformanceLogs.findOneAsync({ clientId, exerciseId }, { sort: { createdAt: 1 } })) || doc
		const payload = {
			exerciseId,
			clientId,
			first,
			last: doc,
		}
		await ExercisePerformance.insertAsync(payload)
	} else {
		const { first, last } = performance
		if (doc.createdAt < first.createdAt || !first.createdAt) {
			await ExercisePerformance.updateAsync({ _id: performance._id }, { $set: { first: doc } })
		}
		if (doc.createdAt > last.createdAt || !last.createdAt) {
			await ExercisePerformance.updateAsync({ _id: performance._id }, { $set: { last: doc } })
		}
	}
})

ExercisePerformanceLogs.after.update(
	async function (userId, doc) {
		const { exerciseId, clientId } = doc
		const performance = await ExercisePerformance.findOneAsync(
			{ clientId, exerciseId },
			{ sort: { createdAt: -1 } },
		)
		if (!performance) return
		const lastPerformance = performance.last

		if (doc.createdAt === lastPerformance.createdAt) {
			const exercise = await Exercises.findOneAsync({
				_id: doc.exerciseId,
			})
			if (!exercise) return
			doc.exerciseName = exercise.text
			await ExercisePerformance.updateAsync({ _id: performance._id }, { $set: { last: doc } })
		}
	},
	{ fetchPrevious: false },
)
