import moment from 'moment-timezone'
import { ClientCoachQuestionnaire } from './clientCoachQuestionnarie'
import { Workouts } from '../workouts/workouts'
import { CoacheQuestionnaire } from '../coachQuestionnarie/coachQuestionnarie'

ClientCoachQuestionnaire.addLinks({
	client: {
		type: 'one',
		collection: Meteor.users,
		field: 'clientId',
		// index: true
	},
	questionnaires: {
		type: 'one',
		collection: CoacheQuestionnaire,
		field: 'questionnaireId',
		// index: true
	},
	coach: {
		type: 'one',
		collection: Meteor.users,
		field: 'coachId',
		// index: true
	},
	workout: {
		type: 'one',
		collection: Workouts,
		field: 'workoutId',
		// index: true
	},
})

ClientCoachQuestionnaire.addReducers({
	dateCreatedAt: {
		body: {
			// Object, dependency graph
			createdAt: 1,
		},
		reduce(object) {
			// return the value
			return moment(object.createdAt).format('LL')
		},
	},
})
