import { Plans } from './plans'

// Replace plans
// Replace Plans

export const getPlans = Plans.createQuery(
	'getPlans',
	{
		$filter({ filters, options, params }) {
			if (params.filters) {
				Object.assign(filters, params.filters)
			}
			if (params.options) {
				Object.assign(options, params.options)
			}
			return { filters, options, params }
		},
		$paginate: true,
		stripe_id: 1,
		amount: 1,
		currency: 1,
		description: 1,
		enabled: 1,
		interval: 1,
		interval_count: 1,
		name: 1,
		dateCreatedAt: 1,
		dateModifiedAt: 1,
	},
	{},
)

export const getActivePlans = Plans.createQuery(
	'getActivePlans',
	{
		$filter({ filters, options, params }) {
			if (Meteor.isServer) {
				const activePlans = Meteor.settings.private.stripe.planIds[params.filters.billingPeriod]
				Object.assign(filters, { stripe_id: { $in: activePlans } })
			} else {
				Object.assign(filters, { stripe_id: '0' })
			}
			return { filters, options, params }
		},
		$paginate: true,
		stripe_id: 1,
		amount: 1,
		currency: 1,
		description: 1,
		enabled: 1,
		interval: 1,
		interval_count: 1,
		name: 1,
		dateCreatedAt: 1,
		dateModifiedAt: 1,
	},
	{},
)
