import { Meteor } from 'meteor/meteor'

const isCdnDisabledViaEnvVar = () => process.env.DISABLE_CDN === 'true' || process.env.DISABLE_CDN === true

if (Meteor.isProduction && Meteor.settings.private.CDN && !isCdnDisabledViaEnvVar()) {
	const CDN_URL = Meteor.settings.private.CDN
	console.log(`cloudFront Setup ${CDN_URL}`)
	WebAppInternals.setBundledJsCssUrlRewriteHook(url => {
		return `${CDN_URL}${url}&_g_app_v_=${process.env.ZCLOUD_IMAGE_VERSION}`
	})

	// WebApp.rawConnectHandlers.use((req, res, next) => {
	// if (
	// 	req._parsedUrl.pathname.match(
	// 		/\.(ttf|ttc|otf|eot|woff|woff2|font\.css|css|js|scss)$/
	// 	)
	// ) {
	// 	res.setHeader('Access-Control-Allow-Origin', "*");
	// 	}
	// 	next();
	// });

	console.log('cloudFront Setup Complete')
}
