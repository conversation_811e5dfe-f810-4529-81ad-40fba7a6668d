import { Exercises } from '../../../api/exercises/exercises'
import { WorkoutDetails } from '../../../api/workoutDetails/workoutDetails'
import { Workouts } from '../../../api/workouts/workouts'
import { CoachClientsNotes } from '../../../api/coachClientsNotes/coachClientsNotes'

const renderNotes = notes => {
	if (notes) {
		const notesList = notes.split('\n')
		return notesList.map((note, index) => {
			if (note.length === 0 || /^( ){1,99}$/g.test(note)) {
				return `<br />`
			}
			if (note.substring(0, 2) === '##') {
				return `<h2>${note.substring(2)}</h2>`
			}
			if (note.substring(0, 1) === '#') {
				return `<h1>${note.substring(1)}</h1>`
			}
			if (/^(-){3,99}$/g.test(note)) {
				return `<hr />`
			}
			if (note.substring(0, 1) === '-' && note.substring(1, 2) !== '-') {
				let html = []

				if (
					index > 0 &&
					notesList[index - 1]?.substring(0, 1) !== '-' &&
					notesList[index - 1]?.substring(0, 1) !== '*'
				) {
					html.push(`<ul>`)
				} else if (index === 0) {
					html.push(`<ul>`)
				}
				html.push(`<li>${note.substring(1)}</li>`)
				if (
					index < notesList.length &&
					notesList[index + 1]?.substring(0, 1) !== '-' &&
					notesList[index + 1]?.substring(0, 1) !== '*'
				) {
					html.push(`</ul>`)
				} else if (index === notesList.length - 1) {
					html.push(`</ul>`)
				}
				return html.join('')
			}
			if (note.substring(0, 1) === '*' && note.substring(1, 2) !== '*') {
				let html = []
				if (
					index > 0 &&
					notesList[index - 1]?.substring(0, 1) !== '*' &&
					notesList[index - 1]?.substring(0, 1) !== '-'
				) {
					html.push(`<ul>`)
				} else if (index === 0) {
					html.push(`<ul>`)
				}
				html.push(`<li class="ql-indent-1">${note.substring(1)}</li>`)
				if (
					index < notesList.length &&
					notesList[index + 1]?.substring(0, 1) !== '*' &&
					notesList[index + 1]?.substring(0, 1) !== '-'
				) {
					html.push(`</ul>`)
				} else if (index === notesList.length - 1) {
					html.push(`</ul>`)
				}
				return html.join('')
			}
			return `<p>${note}</p>`
		})
	}
	return null
}

// Recovery notes from exercises and determine if they are in markdown format and convert them to HTML
export default function upgradeMdNotesToHTML() {
	// const exercises = Exercises.find({notes: {$exists: true, $ne: ''}}).fetch();
	//
	// const bulkOps = exercises.map((exercise) => {
	// 	const notes = exercise.notes;
	// 	if (!notes) {
	// 		console.log('No notes found');
	// 		// console.log(notes);
	// 		return null;
	// 	}
	//
	// 	if( notes.startsWith('<')) {
	// 		console.log('Notes are already in HTML format');
	// 		// console.log(notes);
	// 		return
	// 	}
	// // console.log('---Notes are in markdown format');
	// 	// console.log(notes);
	// 	const notesHTML = renderNotes(notes).join('');
	// 	console.log('Notes converted to HTML');
	// 	// console.log(notesHTML);
	//
	// 	return {
	// 		updateOne: {
	// 			filter: { _id: exercise._id },
	// 			update: { $set: { notes: notesHTML } }
	// 		}
	// 	}
	// });
	// Exercises.rawCollection().bulkWrite(bulkOps.filter(Boolean));
	// console.log(bulkOps.filter(Boolean));
	// upgradeMdNotesToHTMLOnWorkoutDetails();
	// const step = 50000;
	// const max = 25000000;
	//
	//
	// for (let i = 0; i < max; i += step) {
	// 	console.log('Processing from:', i);
	// 	console.log('Processing to:', i + step);
	// 	// upgradeMdNotesToHTMLOnWorkout(step, i);
	// 	// upgradeMdNotesToHTMLOnWorkoutDetails(step, i);
	// }
	// upgradeMdNotesToHTMLOnClientNotes();
}

export async function upgradeMdNotesToHTMLOnWorkoutDetails(limit, skip) {
	const details = await WorkoutDetails.find(
		{
			notes: {
				$exists: true,
				$ne: '',
			},
		},
		{
			limit,
			skip,
		},
	).fetchAsync()
	console.log(details.length)

	const bulkOps = details.map(detail => {
		const notes = detail.notes
		if (!notes) {
			console.log('No notes found')
			return null
		}

		if (notes.startsWith('<')) {
			console.log('Notes are already in HTML format')
			return
		}

		const notesHTML = renderNotes(notes).join('')
		console.log('Notes converted to HTML')
		// console.log(notesHTML);

		return {
			updateOne: {
				filter: { _id: detail._id },
				update: { $set: { notes: notesHTML } },
			},
		}
	})
	// console.log(bulkOps.filter(Boolean));
	// WorkoutDetails.rawCollection().bulkWrite(bulkOps.filter(Boolean));
}

export async function upgradeMdNotesToHTMLOnWorkout(limit, skip) {
	const plans = await Workouts.find(
		{
			notes: {
				$exists: true,
				$ne: '',
			},
		},
		{
			limit,
			skip,
		},
	).fetchAsync()
	console.log(plans.length)

	const bulkOps = plans.map(plan => {
		const notes = plan.notes
		if (!notes) {
			console.log('No notes found')
			return null
		}

		if (notes.startsWith('<')) {
			console.log('Notes are already in HTML format')
			return
		}

		const notesHTML = renderNotes(notes).join('')
		// console.log(notesHTML);
		console.log('Notes converted to HTML')

		return {
			updateOne: {
				filter: { _id: plan._id },
				update: { $set: { notes: notesHTML } },
			},
		}
	})
	// console.log(bulkOps.filter(Boolean));
	// Workouts.rawCollection().bulkWrite(bulkOps.filter(Boolean));
}

export async function upgradeMdNotesToHTMLOnClientNotes(limit, skip) {
	const clients = await CoachClientsNotes.find(
		{ content: { $exists: true, $ne: '' } },
		// {
		// 	limit,
		// 	skip,
		// }
	).fetchAsync()
	console.log(clients.length)

	const bulkOps = clients.map(client => {
		const notes = client.content
		if (!notes) {
			console.log('No notes found')
			return null
		}

		if (notes.startsWith('<')) {
			console.log('Notes are already in HTML format')
			return
		}

		const notesHTML = renderNotes(notes).join('')
		// console.log(notesHTML);
		console.log('Notes converted to HTML')

		return {
			updateOne: {
				filter: { _id: client._id },
				update: { $set: { content: notesHTML } },
			},
		}
	})
	// console.log(bulkOps.filter(Boolean));
	// CoachClientsNotes.rawCollection().bulkWrite(bulkOps.filter(Boolean));
}
