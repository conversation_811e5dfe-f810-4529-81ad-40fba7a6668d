// import { Reminders } from '/imports/api/reminders/reminders'
//
// import identifyMetadata from "../utils/identifyMetadata";
//
// export default async (event) => {
// 	const { invoice, plan, user } = await identifyMetadata(event)
//
//
//
// 	const { invoice, plan, user } = await identifyMetadata(event)
// 	const customerAccountUrl = Meteor.absoluteUrl()+"account"
// 	const adminAccountUrl = `${Meteor.absoluteUrl()}billing/${user._id}`
// 	const user = Meteor.users.findOne({ _id: user._id })
// 	let message = {}
// 	message["user"] = `Hi ${user.profile.firstName}, we had a problem with your payment of ${numeral(invoice.amount_due/100).format('$0,0.00')} USD. Please go to ${customerAccountUrl} at the bottom of the page input your payment info and hit submit, thanks.`
// 	message["admin"] = `Hi, ${user.profile.firstName} had a payment problem of ${numeral(invoice.amount_due/100).format('$0,0.00')} USD. Please go to ${adminAccountUrl} to review.`
// 	message["sms"] = `${Meteor.settings.public.app.name} had a problem with your payment of ${numeral(invoice.amount_due/100).format('$0,0.00')} USD. Please go to ${customerAccountUrl} at the bottom of the page input your payment info and hit submit, thanks.`
//
// 	fromEmail = `info@${Meteor.settings.public.domainBare}`
//
// 	// AddPush "Payment Failed", message["admin"]
//
// 	// AddSendEmailMessage "Payment Failed" , message["user"] , user.emails[0].address
//
// 	const hasReminder = Reminders.find(
// 		{
// 			type:"Subscription",
// 			userId:user._id,
// 			completed:false
// 		}
// 	).count()
// 	if (hasReminder === 0) {
// 		reminder = {
// 			text:`Payment Failed - ${numeral(invoice.amount_due/100).format('$0,0.00')}`,
// 			userId:user._id,
// 			createdAt:moment().valueOf(),
// 			timezone:"America/Dawson",
// 			url:`${Meteor.absoluteUrl()}account`,
// 			type:"Payment",
// 			autoGenerated:true,
// 			pushNotification:false,
// 			interval:0,
// 			intervalCount:0,
// 			intervalMax:0
// 		}
// 		Reminders.insert(reminder)
// 	}
// }
