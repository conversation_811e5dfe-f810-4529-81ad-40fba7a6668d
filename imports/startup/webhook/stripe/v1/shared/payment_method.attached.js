import { onUpgradeSubscription } from '../../../../server/convertKit'
import { Campaigns } from '@collections/campains/campaigns'

const disableNotificationsCampaign = async user => {
	await Campaigns.updateAsync({ coachId: user._id }, { $set: { sent: true } }, { multi: true })
}

export default async event => {
	const { customer } = event.data.object
	const user = await Meteor.users.findOneAsync({
		'stripe.customerId': customer,
	})

	if (!user) {
		return
	}

	await Meteor.users.updateAsync({ _id: user._id }, { $set: { 'stripe.status': 'active' } })
	await onUpgradeSubscription(user.emails[0].address)
	await disableNotificationsCampaign(user)
}
