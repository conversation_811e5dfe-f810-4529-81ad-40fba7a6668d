// import { getMetadataForSubscriptionDeleted } from '../utils/identifyMetadata'
// import { ClientAppConfigs } from '@collections/clientAppConfigs/clientAppConfigs'
// import { removeTag } from '../../../../server/convertKit'
// import { isEmpty } from 'lodash'

export default async event => {
	// const { user, stripeSub: subscriptionId } = await getMetadataForSubscriptionDeleted(event)
	// if (!user || isEmpty(user)) {
	// 	throw new Meteor.Error('Stripe webhook error', 'User not found')
	// }
	// if (!subscriptionId) {
	// 	throw new Meteor.Error('Stripe webhook error', 'Subscription not found')
	// }
	// await Meteor.users.updateAsync(
	// 	{ _id: user._id },
	// 	{
	// 		$unset: { subscription: 1, 'stripe.subscriptionId': 1 },
	// 		$set: { 'stripe.status': 'free' },
	// 	},
	// )
	// const clientAppConfig = await ClientAppConfigs.findOneAsync({ coachId: user._id })
	// if (clientAppConfig) {
	// 	await ClientAppConfigs.updateAsync({ coachId: user._id }, { $set: { isActiveSubscription: false } })
	// }
	// removeTag(user.emails[0].address, 'pro')
}
