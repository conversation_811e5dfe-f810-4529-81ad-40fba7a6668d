import { WebApp } from 'meteor/webapp'
import { Meteor } from 'meteor/meteor'
import bodyParser from 'body-parser'
import Stripe from '../../../services/Stripe'
import { WebHooks } from '../../../server/webhooks'
import * as sharedFns from './shared'

export default (endpoint, secret, fns = {}) => {
	WebApp.rawHandlers.use(`/stripe/v1/${endpoint}`, bodyParser.raw({ type: '*/*' }))
	WebApp.rawHandlers.use(`/stripe/v1/${endpoint}`, async (req, res) => {
		if (req.method !== 'POST') {
			return res.end()
		}
		const signature = req.headers['stripe-signature']
		const event = Stripe.webhooks.constructEvent(req.body, signature, secret)

		try {
			const isAlreadyProcessed = await WebHooks.findOneAsync({
				type: 'stripe',
				succeeded: true,
				'event.id': event.id,
			})
			if (isAlreadyProcessed && !Meteor.settings.private.stripe.ignoreDuplicateWebhooks) {
				return res.end()
			}
			await WebHooks.upsertAsync(
				{
					type: 'stripe',
					'event.id': event.id,
				},
				{ $set: { event } },
			)
			const dir = event.type.replace(/\./gi, '_')
			const fn = fns[dir] || sharedFns[dir]
			if (!fn) {
				await WebHooks.updateAsync(
					{
						type: 'stripe',
						'event.id': event.id,
					},
					{
						$set: {
							succeeded: true,
							nothingToRun: true,
						},
					},
				)
				return res.end()
			}
			fn(event, { Stripe })
			await WebHooks.updateAsync(
				{
					type: 'stripe',
					'event.id': event.id,
				},
				{ $set: { succeeded: true } },
			)
		} catch (err) {
			await WebHooks.updateAsync(
				{
					type: 'stripe',
					'event.id': event.id,
				},
				{ $set: { succeeded: false } },
			)
			res.writeHead(400)
			res.write(err.message)
		} finally {
			res.end()
		}
	})
}
