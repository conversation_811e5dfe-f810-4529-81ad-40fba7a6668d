import { Meteor } from 'meteor/meteor'
import Stripe from 'stripe'

const stripeClient = Stripe(Meteor.settings.private.stripe.secret)

async function getSubscriptions({ status }) {
	let total = 0
	let hasMore = true
	let lastItem = null
	let allSubscriptions = []

	while (hasMore) {
		const subscriptions = await stripeClient.subscriptions.list({
			limit: 100,
			...(lastItem && { starting_after: lastItem }),
			status,
		})
		total += subscriptions.data.length
		hasMore = subscriptions.has_more
		lastItem = subscriptions.data[subscriptions.data.length - 1]?.id
		allSubscriptions.push(...subscriptions.data)
	}

	console.log('Subscriptions ', status, ' total:', total)
	console.log('Subscriptions ', status, ' example:', allSubscriptions[0])
	return allSubscriptions
}

Meteor.startup(async () => {})

export default stripeClient
