import { Accounts } from 'meteor/accounts-base'
import Analytics from '@lib/Analytics/Analytics'

Accounts.onLogin(async () => {
	const location = document.location.href
	if (location.includes('/pt/')) {
	} else {
		await Analytics.login()
		await twoFactor.storeAutologinToken()
	}
})

Accounts.onLogout(async () => {
	await Analytics.logout()
	localStorage.clear()
	sessionStorage.clear()
	document.cookie = 'autologin_token=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;'
	if (window.location.pathname.includes('/app/')) {
		window.location.href = '/app/login'
	}
})
