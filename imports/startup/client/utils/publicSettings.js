import dot from 'dot-object'

/**
 * A utility wrapper for accessing Meteor.settings.public using dot notation
 * @param {string} path - The dot notation path to the setting
 * @param {any} defaultValue - The default value to return if the path doesn't exist
 * @returns {any} The value at the specified path or the default value
 */
export const getPublicSetting = (path, defaultValue = null) => {
	if (!Meteor?.settings?.public) {
		return defaultValue
	}

	const value = dot.pick(path, Meteor.settings.public)
	return value === undefined ? defaultValue : value
}
