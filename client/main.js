import '../imports/startup/client/index'
import React from 'react'
import { createRoot } from 'react-dom/client'
import App from '../imports/ui/both/App'
import createEmotionCache from '../imports/ui/both/createEmotionCache'
import { CacheProvider } from '@emotion/react'
import DisconnectionModal from './DisconnectionModal'

const cache = createEmotionCache()

function Main() {
	return (
		<>
			<CacheProvider value={cache}>
				<DisconnectionModal />
				<App />
			</CacheProvider>
		</>
	)
}

Meteor.startup(() => {
	const $container = document.getElementById('react-target')
	const root = createRoot($container)
	root.render(<Main />)
})
